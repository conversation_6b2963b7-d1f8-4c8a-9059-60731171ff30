import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../device_setting_logic.dart';
import 'common/setting_card_factory.dart';

/// 系统设置区域组件 (使用可扩展的 SettingCardFactory)
class SystemSettingsSection extends StatelessWidget {
  const SystemSettingsSection({super.key});

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<DeviceSettingLogic>();
    // 工厂现在内部处理设备类型
    final factory = SettingCardFactory(logic);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // === 基础系统设置 ===
        factory.createThemeSettingCard(context),
        const SizedBox(height: 6),
        factory.createPowerTriggerSettingCard(context),
        const SizedBox(height: 6),
        factory.createUsbModeSettingCard(context),
        const SizedBox(height: 6),
        factory.createScreenBrightnessSettingCard(context),
        const SizedBox(height: 6),
        factory.createLanguageSettingCard(context),

        // === 显示设置 (D900特有) ===
        const SizedBox(height: 6),
        factory.createVuMeterLevelSettingCard(context),
        const SizedBox(height: 6),
        factory.createVuMeterDisplaySettingCard(context),

        // === 输入设置 ===
        const SizedBox(height: 6),
        factory.createUsbPortSelectSettingCard(context),
        const SizedBox(height: 6),
        factory.createDsdMuteSettingCard(context),

        // === 输出设置 (D900特有) ===
        const SizedBox(height: 6),
        factory.createVolumeStepSettingCard(context),
        const SizedBox(height: 6),
        factory.createPolaritySettingCard(context),
        const SizedBox(height: 6),
        factory.createOutputLevelSettingCard(context),

        // === DX5特有设置 ===
        const SizedBox(height: 6),
        factory.createPcmFilterSettingCard(context),
        const SizedBox(height: 6),
        factory.createHeadphoneGainSettingCard(context),
        const SizedBox(height: 6),
        factory.createLineOutModeSettingCard(context),

        // === 高级设置 ===
        const SizedBox(height: 6),
        factory.createDsdDirectSettingCard(context),
        const SizedBox(height: 6),
        factory.createVolumeMemoryModeSettingCard(context),

        // === 按键自定义 ===
        const SizedBox(height: 6),
        factory.createKeyFunctionSettingCard(context),

        // --- 获取并添加特定于设备的系统设置卡片 ---
        ..._buildDeviceSpecificCards(context, factory),
      ],
    );
  }

  /// 辅助方法，构建特定于设备的系统设置卡片列表
  List<Widget> _buildDeviceSpecificCards(
      BuildContext context, SettingCardFactory factory) {
    // 调用工厂获取特定系统设置卡片列表
    final specificCards = factory.createSystemSpecificSettingCards(context);

    // 如果列表不为空，在每个卡片间添加间距
    if (specificCards.isNotEmpty) {
      List<Widget> spacedCards = [];
      // 在第一个通用卡片（语言）和第一个特定卡片之间加间距
      spacedCards.add(const SizedBox(height: 6));
      for (int i = 0; i < specificCards.length; i++) {
        spacedCards.add(specificCards[i]);
        if (i < specificCards.length - 1) {
          spacedCards.add(const SizedBox(height: 6));
        }
      }
      return spacedCards;
    } else {
      return []; // 没有特定卡片则返回空列表
    }
  }
}
