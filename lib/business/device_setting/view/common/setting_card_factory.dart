import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_ble_control/model/enums/device_mode_type.dart'; // 导入设备类型枚举

import '../../../../../common/util/i18n.dart';
import '../../../../../enums/d900/d900_iis_dsd_channel_type.dart';
import '../../../../../enums/d900/d900_iis_phase_type.dart';
import '../../../../../enums/d900/d900_language_type.dart';
import '../../../../../enums/d900/d900_power_trigger_type.dart';
import '../../../../../enums/d900/d900_screen_brightness_type.dart';
import '../../../../../enums/d900/d900_theme_type.dart';
import '../../../../../enums/d900/d900_usb_select_type.dart';
import '../../../../../enums/d900/d900_usb_type.dart';
import '../../../../../enums/dx5/dx5_decode_mode_type.dart';
import '../../../../../enums/dx5/dx5_filter_parameter_type.dart';
import '../../../../../enums/dx5/dx5_language_type.dart';
import '../../../../../enums/dx5/dx5_multi_function_key_type.dart';
import '../../../../../enums/dx5/dx5_power_trigger_type.dart';
import '../../../../../enums/dx5/dx5_screen_brightness_type.dart';
import '../../../../../enums/dx5/dx5_theme_type.dart';
import '../../../../../enums/dx5/dx5_usb_type.dart';
import '../../../../../models/d900_device_settings.dart';
import '../../../../../models/dx5_device_settings.dart';
import '../../device_setting_logic.dart';
import '../setting_switch.dart';
import '../setting_card.dart';
import '../setting_dropdown.dart';
import 'enum_setting_card.dart';

// --- 移到顶层 ---
// 定义通用卡片创建函数的类型别名
typedef SettingCardCreator = Widget Function(BuildContext);

/// 设置卡片工厂 (可扩展版)
/// 根据设备类型创建适合的设置卡片
class SettingCardFactory {
  final DeviceSettingLogic logic;
  final DeviceModeType deviceType;

  // --- 通用 Enum 卡片创建 Map ---
  late final Map<DeviceModeType, SettingCardCreator> _themeCardCreators;
  late final Map<DeviceModeType, SettingCardCreator> _powerTriggerCardCreators;
  late final Map<DeviceModeType, SettingCardCreator> _usbModeCardCreators;
  late final Map<DeviceModeType, SettingCardCreator> _brightnessCardCreators;
  late final Map<DeviceModeType, SettingCardCreator> _languageCardCreators;

  // --- 设备特定卡片创建 Map ---
  // 分为系统设置和音频设置两类
  late final Map<DeviceModeType, List<SettingCardCreator>>
      _systemSpecificCardCreators;
  late final Map<DeviceModeType, List<SettingCardCreator>>
      _audioSpecificCardCreators;

  SettingCardFactory(this.logic) : deviceType = logic.deviceType {
    _initializeCreators();
  }

  /// 初始化所有卡片创建器 Map
  void _initializeCreators() {
    // 通用设置
    _themeCardCreators = {
      DeviceModeType.dx9: (context) => EnumSettingCard<D900ThemeType>(
            title: l10n.theme,
            getValue: (logic) =>
                logic.state.settings.value?.theme as D900ThemeType?,
            options: D900ThemeType.values,
            onChanged: (theme) => logic.setTheme(theme),
            defaultValue: D900ThemeType.values.first,
          ),
      DeviceModeType.dx5: (context) => EnumSettingCard<Dx5ThemeType>(
            title: l10n.theme,
            getValue: (logic) =>
                logic.state.settings.value?.theme as Dx5ThemeType?,
            options: Dx5ThemeType.values,
            onChanged: (theme) => logic.setTheme(theme),
            defaultValue: Dx5ThemeType.values.first,
          ),
    };
    _powerTriggerCardCreators = {
      DeviceModeType.dx9: (context) => EnumSettingCard<D900PowerTriggerType>(
            title: l10n.powerTrigger,
            getValue: (logic) => logic.state.settings.value?.powerTrigger
                as D900PowerTriggerType?,
            options: D900PowerTriggerType.values,
            onChanged: (trigger) => logic.setPowerTrigger(trigger),
            defaultValue: D900PowerTriggerType.signal,
          ),
      DeviceModeType.dx5: (context) => EnumSettingCard<Dx5PowerTriggerType>(
            title: l10n.powerTrigger,
            getValue: (logic) => logic.state.settings.value?.powerTrigger
                as Dx5PowerTriggerType?,
            options: Dx5PowerTriggerType.values,
            onChanged: (trigger) => logic.setPowerTrigger(trigger),
            defaultValue: Dx5PowerTriggerType.signal,
          ),
    };
    _usbModeCardCreators = {
      DeviceModeType.dx9: (context) => EnumSettingCard<D900UsbType>(
            title: l10n.usbMode,
            getValue: (logic) =>
                logic.state.settings.value?.usbType as D900UsbType?,
            options: D900UsbType.values,
            onChanged: (usbMode) => logic.setUsbMode(usbMode),
            defaultValue: D900UsbType.values.first,
          ),
      DeviceModeType.dx5: (context) => EnumSettingCard<Dx5UsbType>(
            title: l10n.usbMode,
            getValue: (logic) =>
                logic.state.settings.value?.usbType as Dx5UsbType?,
            options: Dx5UsbType.values,
            onChanged: (usbMode) => logic.setUsbMode(usbMode),
            defaultValue: Dx5UsbType.values.first,
          ),
    };
    _brightnessCardCreators = {
      DeviceModeType.dx9: (context) =>
          EnumSettingCard<D900ScreenBrightnessType>(
            title: l10n.screenBrightness,
            getValue: (logic) => logic.state.settings.value?.screenBrightness
                as D900ScreenBrightnessType?,
            options: D900ScreenBrightnessType.values,
            onChanged: (brightness) => logic.setScreenBrightness(brightness),
            defaultValue: D900ScreenBrightnessType.values.first,
          ),
      DeviceModeType.dx5: (context) => EnumSettingCard<Dx5ScreenBrightnessType>(
            title: l10n.screenBrightness,
            getValue: (logic) => logic.state.settings.value?.screenBrightness
                as Dx5ScreenBrightnessType?,
            options: Dx5ScreenBrightnessType.values,
            onChanged: (brightness) => logic.setScreenBrightness(brightness),
            defaultValue: Dx5ScreenBrightnessType.values.first,
          ),
    };
    _languageCardCreators = {
      DeviceModeType.dx9: (context) => EnumSettingCard<D900LanguageType>(
            title: l10n.language,
            getValue: (logic) =>
                logic.state.settings.value?.language as D900LanguageType?,
            options: D900LanguageType.values,
            onChanged: (language) => logic.setLanguage(language),
            defaultValue: D900LanguageType.values.first,
          ),
      DeviceModeType.dx5: (context) => EnumSettingCard<Dx5LanguageType>(
            title: l10n.language,
            getValue: (logic) =>
                logic.state.settings.value?.language as Dx5LanguageType?,
            options: Dx5LanguageType.values,
            onChanged: (language) => logic.setLanguage(language),
            defaultValue: Dx5LanguageType.values.first,
          ),
    };

    // 设备特定设置 - 系统设置部分
    _systemSpecificCardCreators = {
      DeviceModeType.dx9: [
        _createUsbSelectCard,
      ],
      DeviceModeType.dx5: [
        _createMultiFunctionKeyCard,
        // DX5 系统设置特有项
      ],
      // 为 unknown 类型提供空列表
      DeviceModeType.unknown: [],
    };

    // 设备特定设置 - 音频设置部分
    _audioSpecificCardCreators = {
      DeviceModeType.dx9: [
        _createIisPhaseCard,
        _createIisDsdChannelCard,
        // D900 音频设置特有项
      ],
      DeviceModeType.dx5: [
        _createFilterCard,
        _createDecodeModeCard,
        // DX5 音频设置特有项
      ],
      // 为 unknown 类型提供空列表
      DeviceModeType.unknown: [],
    };
  }

  // --- 公共创建方法 ---

  /// 创建主题设置卡片
  Widget createThemeSettingCard(BuildContext context) {
    final creator = _themeCardCreators[deviceType];
    return creator != null ? creator(context) : const SizedBox();
  }

  /// 创建电源触发设置卡片
  Widget createPowerTriggerSettingCard(BuildContext context) {
    final creator = _powerTriggerCardCreators[deviceType];
    return creator != null ? creator(context) : const SizedBox();
  }

  /// 创建USB模式设置卡片
  Widget createUsbModeSettingCard(BuildContext context) {
    // 确保使用与其他设置项相同的样式
    final creator = _usbModeCardCreators[deviceType];
    return creator != null ? creator(context) : const SizedBox();
  }

  /// 创建屏幕亮度设置卡片
  Widget createScreenBrightnessSettingCard(BuildContext context) {
    final creator = _brightnessCardCreators[deviceType];
    return creator != null ? creator(context) : const SizedBox();
  }

  /// 创建语言设置卡片
  Widget createLanguageSettingCard(BuildContext context) {
    final creator = _languageCardCreators[deviceType];
    return creator != null ? creator(context) : const SizedBox();
  }

  /// 创建音频监听设置卡片 (通用 Switch) - 移除 Obx
  Widget createAudioMonitoringSettingCard(BuildContext context) {
    // View 层应负责 Obx
    // --- 添加 SettingCard 包裹 ---
    return SettingCard(
      title: l10n.audioMonitoring, // Title for accessibility/semantics
      showTitle: false, // Hide title visually if redundant
      child: SettingSwitch(
        title: l10n.audioMonitoring,
        // value 依赖 state，View 层需要 Obx
        value: logic.state.settings.value?.audioBluetooth ?? false,
        onChanged: (enabled) => logic.toggleAudioMonitoring(enabled),
      ),
    );
  }

  /// 创建蓝牙 AptX 设置卡片 (通用 Switch) - 移除 Obx
  Widget createBluetoothAptxSettingCard(BuildContext context) {
    // View 层应负责 Obx
    // --- 添加 SettingCard 包裹 ---
    return SettingCard(
      title: l10n.bluetoothAptx,
      showTitle: false,
      child: SettingSwitch(
        title: l10n.bluetoothAptx,
        // value 依赖 state，View 层需要 Obx
        value: logic.state.settings.value?.bluetoothAPTX ?? false,
        onChanged: (enabled) => logic.toggleBluetoothAptx(enabled),
      ),
    );
  }

  /// 创建USB DSD直通设置卡片 (D900特有)
  Widget createUsbDsdPassthroughSettingCard(BuildContext context) {
    // View 层应负责 Obx
    return SettingCard(
      title: l10n.usbDsdPassthrough,
      showTitle: false,
      child: SettingSwitch(
        title: l10n.usbDsdPassthrough,
        value: logic.state.settings.value?.usbDsdPassthrough ?? false,
        onChanged: (enabled) => logic.setUsbDsdPassthrough(enabled),
      ),
    );
  }

  /// 创建特定于当前设备的系统设置卡片列表
  List<Widget> createSystemSpecificSettingCards(BuildContext context) {
    final creators = _systemSpecificCardCreators[deviceType] ?? [];
    return creators.map((creator) => creator(context)).toList();
  }

  /// 创建特定于当前设备的音频设置卡片列表
  List<Widget> createAudioSpecificSettingCards(BuildContext context) {
    final creators = _audioSpecificCardCreators[deviceType] ?? [];
    return creators.map((creator) => creator(context)).toList();
  }

  /// 创建VU表0dB幅值设置卡片 (D900特有) - 简化版本
  Widget createVuMeterLevelSettingCard(BuildContext context) {
    if (!logic.isD900Device) return SizedBox.shrink();

    final vuLevelOptions = ['+4dBu', '+10dBu'];

    return Obx(() {
      final settings = logic.state.settings.value;
      if (settings == null || settings is! D900DeviceSettings) {
        return SizedBox.shrink();
      }

      return SettingCard(
        title: 'VU表0dB幅值',
        child: SettingDropdown(
          title: '',
          value: vuLevelOptions[0], // 默认值，需要根据实际设置调整
          onPressed: () {
            // 显示选择对话框
            _showSelectionDialog(context, 'VU表0dB幅值', vuLevelOptions, (index) {
              logic.setVuMeterLevel(index);
            });
          },
        ),
      );
    });
  }

  /// 创建VU条显示设置卡片 (D900特有) - 简化版本
  Widget createVuMeterDisplaySettingCard(BuildContext context) {
    if (!logic.isD900Device) return SizedBox.shrink();

    final vuDisplayOptions = ['全开', '常规界面', 'FFT界面', '全关'];

    return Obx(() {
      final settings = logic.state.settings.value;
      if (settings == null || settings is! D900DeviceSettings) {
        return SizedBox.shrink();
      }

      return SettingCard(
        title: 'VU条显示模式',
        child: SettingDropdown(
          title: '',
          value: vuDisplayOptions[0], // 默认值，需要根据实际设置调整
          onPressed: () {
            _showSelectionDialog(context, 'VU条显示模式', vuDisplayOptions, (index) {
              logic.setVuMeterDisplay(index);
            });
          },
        ),
      );
    });
  }

  /// 创建USB接口选择设置卡片 (D900特有) - 简化版本
  Widget createUsbPortSelectSettingCard(BuildContext context) {
    if (!logic.isD900Device) return SizedBox.shrink();

    final usbPortOptions = ['Type C', 'Type B', '自动'];

    return Obx(() {
      final settings = logic.state.settings.value;
      if (settings == null || settings is! D900DeviceSettings) {
        return SizedBox.shrink();
      }

      return SettingCard(
        title: 'USB接口选择',
        child: SettingDropdown(
          title: '',
          value: usbPortOptions[2], // 默认自动
          onPressed: () {
            _showSelectionDialog(context, 'USB接口选择', usbPortOptions, (index) {
              logic.setUsbPortSelect(index);
            });
          },
        ),
      );
    });
  }

  /// 创建DSD MUTE设置卡片 (D900特有) - 简化版本
  Widget createDsdMuteSettingCard(BuildContext context) {
    if (!logic.isD900Device) return SizedBox.shrink();

    final dsdMuteOptions = ['高电平有效', '低电平有效', '关'];

    return Obx(() {
      final settings = logic.state.settings.value;
      if (settings == null || settings is! D900DeviceSettings) {
        return SizedBox.shrink();
      }

      return SettingCard(
        title: 'DSD MUTE',
        child: SettingDropdown(
          title: '',
          value: dsdMuteOptions[0], // 默认高电平有效
          onPressed: () {
            _showSelectionDialog(context, 'DSD MUTE', dsdMuteOptions, (index) {
              logic.setDsdMute(index);
            });
          },
        ),
      );
    });
  }

  /// 创建音量步进设置卡片 (D900特有) - 简化版本
  Widget createVolumeStepSettingCard(BuildContext context) {
    if (!logic.isD900Device) return SizedBox.shrink();

    final volumeStepOptions = ['0.5dB', '1dB'];

    return Obx(() {
      final settings = logic.state.settings.value;
      if (settings == null || settings is! D900DeviceSettings) {
        return SizedBox.shrink();
      }

      return SettingCard(
        title: '音量步进',
        child: SettingDropdown(
          title: '',
          value: volumeStepOptions[0], // 默认0.5dB
          onPressed: () {
            _showSelectionDialog(context, '音量步进', volumeStepOptions, (index) {
              logic.setVolumeStep(index);
            });
          },
        ),
      );
    });
  }

  /// 创建极性设置卡片 (D900特有) - 简化版本
  Widget createPolaritySettingCard(BuildContext context) {
    if (!logic.isD900Device) return SizedBox.shrink();

    final polarityOptions = ['标准', '反相'];

    return Obx(() {
      final settings = logic.state.settings.value;
      if (settings == null || settings is! D900DeviceSettings) {
        return SizedBox.shrink();
      }

      return SettingCard(
        title: '极性',
        child: SettingDropdown(
          title: '',
          value: polarityOptions[0], // 默认标准
          onPressed: () {
            _showSelectionDialog(context, '极性', polarityOptions, (index) {
              logic.setPolarity(index);
            });
          },
        ),
      );
    });
  }

  /// 创建输出幅值设置卡片 (D900特有) - 简化版本
  Widget createOutputLevelSettingCard(BuildContext context) {
    if (!logic.isD900Device) return SizedBox.shrink();

    final outputLevelOptions = ['5V', '4V'];

    return Obx(() {
      final settings = logic.state.settings.value;
      if (settings == null || settings is! D900DeviceSettings) {
        return SizedBox.shrink();
      }

      return SettingCard(
        title: '输出幅值',
        child: SettingDropdown(
          title: '',
          value: outputLevelOptions[0], // 默认5V
          onPressed: () {
            _showSelectionDialog(context, '输出幅值', outputLevelOptions, (index) {
              logic.setOutputLevel(index);
            });
          },
        ),
      );
    });
  }

  /// 创建PCM滤波器设置卡片 (DX5II特有) - 简化版本
  Widget createPcmFilterSettingCard(BuildContext context) {
    if (logic.isD900Device) return SizedBox.shrink();

    final pcmFilterOptions = [
      'F-1',
      'F-2',
      'F-3',
      'F-4',
      'F-5',
      'F-6',
      'F-7',
      'F-8'
    ];

    return Obx(() {
      final settings = logic.state.settings.value;
      if (settings == null || settings is! Dx5DeviceSettings) {
        return SizedBox.shrink();
      }

      return SettingCard(
        title: 'PCM滤波器',
        child: SettingDropdown(
          title: '',
          value: 'F-1', // 默认F-1
          onPressed: () {
            _showSelectionDialog(context, 'PCM滤波器', pcmFilterOptions, (index) {
              logic.setPcmFilter(index);
            });
          },
        ),
      );
    });
  }

  /// 创建耳放增益设置卡片 (DX5II特有) - 简化版本
  Widget createHeadphoneGainSettingCard(BuildContext context) {
    if (logic.isD900Device) return SizedBox.shrink();

    final headphoneGainOptions = ['低', '高'];

    return Obx(() {
      final settings = logic.state.settings.value;
      if (settings == null || settings is! Dx5DeviceSettings) {
        return SizedBox.shrink();
      }

      return SettingCard(
        title: l10n.headphoneGain,
        child: SettingDropdown(
          title: '',
          value: headphoneGainOptions[0], // 默认低
          onPressed: () {
            _showSelectionDialog(
                context, l10n.headphoneGain, headphoneGainOptions, (index) {
              logic.setHeadphoneGain(index);
            });
          },
        ),
      );
    });
  }

  /// 创建线路模式设置卡片 (DX5II特有) - 简化版本
  Widget createLineOutModeSettingCard(BuildContext context) {
    if (logic.isD900Device) return SizedBox.shrink();

    final lineOutModeOptions = ['前级', 'DAC'];

    return Obx(() {
      final settings = logic.state.settings.value;
      if (settings == null || settings is! Dx5DeviceSettings) {
        return SizedBox.shrink();
      }

      return SettingCard(
        title: '线路模式',
        child: SettingDropdown(
          title: '',
          value: lineOutModeOptions[0], // 默认前级
          onPressed: () {
            _showSelectionDialog(context, '线路模式', lineOutModeOptions, (index) {
              logic.setLineOutMode(index);
            });
          },
        ),
      );
    });
  }

  /// 创建PEQ启用设置卡片 - 简化版本
  Widget createPeqEnabledSettingCard(BuildContext context) {
    return Obx(() {
      final settings = logic.state.settings.value;
      if (settings == null) return SizedBox.shrink();

      return SettingCard(
        title: 'PEQ启用',
        child: SettingSwitch(
          title: 'PEQ启用',
          value: false, // 默认禁用，需要根据实际设置调整
          onChanged: (value) {
            logic.setPeqEnabled(value);
          },
        ),
      );
    });
  }

  /// 创建PEQ预设设置卡片 - 简化版本
  Widget createPeqPresetSettingCard(BuildContext context) {
    final peqPresetOptions = ['Bass1', 'Bass2', 'Airy', 'Warm', 'Dynamic'];

    return Obx(() {
      final settings = logic.state.settings.value;
      if (settings == null) return SizedBox.shrink();

      return SettingCard(
        title: 'PEQ预设',
        child: SettingDropdown(
          title: '',
          value: peqPresetOptions[2], // 默认Airy
          onPressed: () {
            _showSelectionDialog(context, 'PEQ预设', peqPresetOptions, (index) {
              logic.setPeqPreset(index);
            });
          },
        ),
      );
    });
  }

  /// 创建DSD直通设置卡片 (D900特有) - 简化版本
  Widget createDsdDirectSettingCard(BuildContext context) {
    if (!logic.isD900Device) return SizedBox.shrink();

    return Obx(() {
      final settings = logic.state.settings.value;
      if (settings == null || settings is! D900DeviceSettings) {
        return SizedBox.shrink();
      }

      return SettingCard(
        title: 'DSD直通',
        child: SettingSwitch(
          title: 'DSD直通',
          value: true, // 默认启用，需要根据实际设置调整
          onChanged: (value) {
            logic.setDsdDirect(value);
          },
        ),
      );
    });
  }

  /// 创建音量记忆方式设置卡片 - 简化版本
  Widget createVolumeMemoryModeSettingCard(BuildContext context) {
    final memoryModeOptions = ['跟随输入', '跟随输出', '无'];

    return Obx(() {
      final settings = logic.state.settings.value;
      if (settings == null) return SizedBox.shrink();

      return SettingCard(
        title: '音量记忆方式',
        child: SettingDropdown(
          title: '',
          value: memoryModeOptions[1], // 默认跟随输出
          onPressed: () {
            _showSelectionDialog(context, '音量记忆方式', memoryModeOptions, (index) {
              logic.setVolumeMemoryMode(index);
            });
          },
        ),
      );
    });
  }

  /// 创建PEQ记忆方式设置卡片 - 简化版本
  Widget createPeqMemoryModeSettingCard(BuildContext context) {
    final memoryModeOptions = ['跟随输入', '跟随输出', '无'];

    return Obx(() {
      final settings = logic.state.settings.value;
      if (settings == null) return SizedBox.shrink();

      return SettingCard(
        title: 'PEQ记忆方式',
        child: SettingDropdown(
          title: '',
          value: memoryModeOptions[1], // 默认跟随输出
          onPressed: () {
            _showSelectionDialog(context, 'PEQ记忆方式', memoryModeOptions,
                (index) {
              logic.setPeqMemoryMode(index);
            });
          },
        ),
      );
    });
  }

  /// 创建按键功能自定义设置卡片 - 简化版本
  Widget createKeyFunctionSettingCard(BuildContext context) {
    final keyFunctionOptions = logic.isD900Device
        ? ['输入选择', '输出选择', '主页选择', '亮度选择', '息屏', '静音', 'EQ选择', '开关机触发选择']
        : [
            '输入选择',
            '输出选择',
            '主页选择',
            '亮度选择',
            '息屏',
            '静音',
            'EQ选择',
            '开关机触发选择',
            'PCM滤波器选择',
            '耳放增益'
          ];

    return Column(
      children: [
        // 主按键功能
        Obx(() {
          final settings = logic.state.settings.value;
          if (settings == null) return SizedBox.shrink();

          return SettingCard(
            title: '主按键功能',
            child: SettingDropdown(
              title: '',
              value: keyFunctionOptions[1], // 默认输出选择
              onPressed: () {
                _showSelectionDialog(context, '主按键功能', keyFunctionOptions,
                    (index) {
                  logic.setMainKeyFunction(index);
                });
              },
            ),
          );
        }),
        SizedBox(height: 6),
        // 遥控A键功能
        Obx(() {
          final settings = logic.state.settings.value;
          if (settings == null) return SizedBox.shrink();

          return SettingCard(
            title: '遥控A键功能',
            child: SettingDropdown(
              title: '',
              value: keyFunctionOptions[1], // 默认输出选择
              onPressed: () {
                _showSelectionDialog(context, '遥控A键功能', keyFunctionOptions,
                    (index) {
                  logic.setRemoteAKeyFunction(index);
                });
              },
            ),
          );
        }),
        SizedBox(height: 6),
        // 遥控B键功能
        Obx(() {
          final settings = logic.state.settings.value;
          if (settings == null) return SizedBox.shrink();

          return SettingCard(
            title: '遥控B键功能',
            child: SettingDropdown(
              title: '',
              value: keyFunctionOptions[1], // 默认输出选择
              onPressed: () {
                _showSelectionDialog(context, '遥控B键功能', keyFunctionOptions,
                    (index) {
                  logic.setRemoteBKeyFunction(index);
                });
              },
            ),
          );
        }),
      ],
    );
  }

  // --- 内部私有创建方法 (用于设备特定设置) ---

  /// 创建多功能键设置卡片 (DX5 特有)
  Widget _createMultiFunctionKeyCard(BuildContext context) {
    return EnumSettingCard<Dx5MultiFunctionKeyType>(
      title: l10n.multiFunctionKey,
      getValue: (logic) => logic.state.settings.value?.multiFunctionKey
          as Dx5MultiFunctionKeyType?,
      options: Dx5MultiFunctionKeyType.values,
      onChanged: (key) => logic.setMultiFunctionKey(key),
      defaultValue: Dx5MultiFunctionKeyType.inputSelect,
    );
  }

  /// 创建滤波器设置卡片 (DX5 特有)
  Widget _createFilterCard(BuildContext context) {
    return EnumSettingCard<Dx5FilterParameterType>(
      title: l10n.filter,
      getValue: (logic) =>
          logic.state.settings.value?.filter as Dx5FilterParameterType?,
      options: Dx5FilterParameterType.values,
      onChanged: (filter) => logic.setFilter(filter),
      defaultValue: Dx5FilterParameterType.minimumPhase,
    );
  }

  /// 创建解码模式设置卡片 (DX5 特有)
  Widget _createDecodeModeCard(BuildContext context) {
    return EnumSettingCard<Dx5DecodeModeType>(
      title: l10n.decodeMode,
      getValue: (logic) =>
          logic.state.settings.value?.decodeMode as Dx5DecodeModeType?,
      options: Dx5DecodeModeType.values,
      onChanged: (mode) => logic.setDecodeMode(mode),
      defaultValue: Dx5DecodeModeType.prefix,
    );
  }

  /// 创建USB选择设置卡片 (D900特有)
  Widget _createUsbSelectCard(BuildContext context) {
    // 确保使用与其他设置项相同的样式
    return EnumSettingCard<D900UsbSelectType>(
      title: l10n.usbSelect,
      getValue: (logic) =>
          logic.state.settings.value?.usbSelect as D900UsbSelectType?,
      options: D900UsbSelectType.values,
      onChanged: (usbSelect) => logic.setUsbSelect(usbSelect),
      defaultValue: D900UsbSelectType.values.first,
    );
  }

  /// 创建IIS相位设置卡片 (D900特有)
  Widget _createIisPhaseCard(BuildContext context) {
    return EnumSettingCard<D900IisPhaseType>(
      title: l10n.iisPhase,
      getValue: (logic) =>
          logic.state.settings.value?.iisPhase as D900IisPhaseType?,
      options: D900IisPhaseType.values,
      onChanged: (phase) => logic.setIisPhase(phase),
      defaultValue: D900IisPhaseType.values.first,
    );
  }

  /// 创建IIS DSD通道设置卡片 (D900特有)
  Widget _createIisDsdChannelCard(BuildContext context) {
    return EnumSettingCard<D900IisDsdChannelType>(
      title: l10n.iisDsdChannel,
      getValue: (logic) =>
          logic.state.settings.value?.iisDsdChannel as D900IisDsdChannelType?,
      options: D900IisDsdChannelType.values,
      onChanged: (channel) => logic.setIisDsdChannel(channel),
      defaultValue: D900IisDsdChannelType.values.first,
    );
  }

  /// 显示选择对话框的辅助方法
  void _showSelectionDialog(BuildContext context, String title,
      List<String> options, Function(int) onSelected) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: options.asMap().entries.map((entry) {
            final index = entry.key;
            final option = entry.value;
            return ListTile(
              title: Text(option),
              onTap: () {
                Navigator.of(context).pop();
                onSelected(index);
              },
            );
          }).toList(),
        ),
      ),
    );
  }
}
