import 'dart:async';

import 'package:get/get.dart';
import 'package:topping_ble_control/device/device_factory.dart';
import 'package:topping_ble_control/model/enums/ble_connection_state.dart';
import 'package:topping_home/common/util/i18n.dart';
import 'package:topping_home/common/util/log_util.dart';
import 'package:topping_home/factories/device_settings_factory.dart';
import 'package:topping_home/models/d900_device_settings.dart';
import 'package:topping_home/router/routers.dart';
import 'package:topping_home/theme/color_palettes.dart';

import '../../enums/d900/d900_display_type.dart';
import '../../enums/d900/d900_input_type.dart';
import '../../enums/d900/d900_output_type.dart';
import '../../enums/dx5/dx5_display_type.dart';
import '../../enums/dx5/dx5_input_type.dart';
import '../../enums/dx5/dx5_output_type.dart';
import '../../models/dx5_device_settings.dart';
import '../../repositories/background_repository.dart';
import '../../repositories/device_repository.dart';
import '../../service/firmware_service.dart';
import '../../utils/device_event_synchronizer.dart'; // Import the new synchronizer
import '../peq/peq_bindings.dart';
import '../peq/peq_controller.dart';
import 'device_detail_state.dart';

/// 设备详情逻辑
class DeviceDetailLogic extends GetxController {
  final DeviceDetailState state = DeviceDetailState();

  final DeviceRepository storage = Get.find<DeviceRepository>();
  final BackgroundRepository backgroundManager =
      Get.find<BackgroundRepository>();
  final FirmwareService _firmwareService = Get.find<FirmwareService>();

  // 使用 DeviceFactory
  final DeviceFactory _deviceFactory = DeviceFactory();

  // 获取当前设备管理器 (只读，可能为 null)
  DeviceManager? get _deviceManager => _deviceFactory.currentDeviceManager;

  // 判断是否为 D900 设备
  bool get isD900Device => state.isD900Device;

  // 事件同步器实例
  DeviceEventSynchronizer? _eventSynchronizer;

  // 仍然需要监听连接状态
  StreamSubscription? _connectionSubscription;

  // 设置更新流订阅
  StreamSubscription? _settingsSubscription;

  @override
  void onInit() {
    super.onInit();

    // 初始化PEQ相关依赖
    initPEQDependencies();
    _initBackgroundSettings();
    _loadDeviceSettings();
    _subscribeToSettingsUpdates(); // 订阅设置更新流

    // 订阅蓝牙连接状态事件
    _subscribeConnectionEvents();

    // 暂时先假设连接时会收到事件。
    if (_deviceManager != null) {
      Log.i(
          'DeviceDetailLogic: Initial state seems connected, starting event synchronizer.');
      _startEventSynchronizer();
    }
  }

  // 订阅蓝牙连接状态事件
  void _subscribeConnectionEvents() {
    _connectionSubscription?.cancel(); // 取消之前的订阅
    _connectionSubscription = _deviceFactory.connectionState.listen((event) {
      Log.i('设备连接状态变化: ${event.state}');
      state.isConnected.value = event.state == BleConnectionState.connected;

      if (state.isConnected.value) {
        Log.i('设备已连接，开始同步设置并启动事件同步器');
        _syncDeviceSettings(); // 请求初始设置
        _startEventSynchronizer(); // 启动事件同步器
      } else {
        Log.i('设备已断开，停止事件同步器');
        _stopEventSynchronizer(); // 停止事件同步器
        // 处理断开连接时的 UI 状态 ，返回首页
        Get.back();
      }
    });
  }

  /// 启动事件同步器
  void _startEventSynchronizer() {
    _eventSynchronizer?.stopListening(); // 先停止旧的（如果有）
    final manager = _deviceManager;
    if (manager != null && state.deviceId.isNotEmpty) {
      _eventSynchronizer = DeviceEventSynchronizer(
        deviceId: state.deviceId,
        manager: manager,
      );
      _eventSynchronizer!.startListening();
      Log.i('Event synchronizer started.');
    } else {
      Log.w(
          'Cannot start event synchronizer: manager is null or deviceId is empty.');
    }
  }

  /// 停止事件同步器
  void _stopEventSynchronizer() {
    _eventSynchronizer?.stopListening();
    _eventSynchronizer = null;
    Log.i('Event synchronizer stopped.');
  }

  /// 执行设备操作的通用辅助方法（替代原来的_executeCommandHelper）
  Future<void> _executeDeviceAction(
      String actionName, Future<void> Function(DeviceManager) action) async {
    final manager = _deviceManager;
    if (manager == null) {
      Log.w("无法执行 $actionName：设备未连接");
      Get.snackbar('错误', '设备未连接');
      return;
    }

    try {
      Log.i("执行操作: $actionName");
      await action(manager);
      Log.i("操作 $actionName 已完成");
    } catch (e) {
      Log.e("执行操作 $actionName 时出错: $e");
      Get.snackbar('错误', '操作失败: $e');
    }
  }

  // ---------- 音量转换辅助方法 ----------

  /// 将内部音量值转换为用于发送到设备的值
  /// @param volume 内部音量值 (DX5: -99到0, D900: -99到+8)
  /// @return 发送到设备的值
  int convertVolumeToDeviceValue(int volume) {
    Log.i("【音量调试】convertVolumeToDeviceValue - 输入音量值: $volume, 设备类型是否为D900: $isD900Device");
    int result;
    if (isD900Device) {
      // D900设备规则：-99到0的值发送正值（取绝对值），+1到+8的值发送负值
      if (volume <= 0) {
        // 对于-99到0的范围，发送正值（取绝对值）
        result = volume.abs();
        Log.i("【音量调试】D900设备转换音量值(负值或零): $volume -> $result");
      } else {
        // 对于+1到+8的范围，发送负值
        result = -volume;
        Log.i("【音量调试】D900设备转换音量值(正值): $volume -> $result");
      }
    } else {
      // DX5设备：所有值都转为正值发送
      result = -volume; // 对于负值，转换为正值发送
      Log.i("【音量调试】DX5设备转换音量值: $volume -> $result");
    }
    return result;
  }

  /// 将从设备接收到的值转换为内部音量值
  /// @param deviceValue 从设备接收到的值
  /// @return 内部音量值 (DX5: -99到0, D900: -99到+8)
  int convertDeviceValueToVolume(int deviceValue) {
    if (isD900Device) {
      // D900设备规则
      if (deviceValue >= 0) {
        // 正值对应-99到0的范围
        return -deviceValue;
      } else {
        // 负值对应+1到+8的范围
        return -deviceValue;
      }
    } else {
      // DX5设备：所有从设备收到的值都是正值，转为负值
      return -deviceValue;
    }
  }

  /// 获取设备音量的有效范围
  /// @return [最小值, 最大值]
  List<int> getDeviceVolumeRange() {
    if (isD900Device) {
      return [-99, 8]; // D900: -99到+8 dB
    } else {
      return [-99, 0]; // DX5: -99到0 dB
    }
  }

  /// 设置音量 (UI 拖动结束时调用)
  void setVolumeEnd(int volume) {
    Log.i("【音量调试】setVolumeEnd - 输入音量值: $volume");
    // 根据设备类型调整音量范围
    var range = getDeviceVolumeRange();
    int adjustedVolume = volume.clamp(range[0], range[1]);

    if (adjustedVolume != volume) {
      Log.i("【音量调试】将音量限制在有效范围内: 原值=$volume, 调整后=$adjustedVolume, 范围=$range");
      Log.d("将音量限制在有效范围内: 原值=$volume, 调整后=$adjustedVolume");
    }

    // 转换为设备值
    int deviceVolume = convertVolumeToDeviceValue(adjustedVolume);
    Log.i("【音量调试】设置音量: 内部值=$adjustedVolume dB, 发送值=$deviceVolume, 设备类型是否为D900: $isD900Device");
    Log.i("设置音量: 内部值=$adjustedVolume dB, 发送值=$deviceVolume");

    // 直接调用设备管理器方法
    _executeDeviceAction("设置音量为 $adjustedVolume dB (发送值: $deviceVolume)",
        (manager) async {
      Log.i("【音量调试】调用设备管理器设置音量: $deviceVolume, 管理器类型: ${manager.runtimeType}");
      manager.setVolume(deviceVolume);
      Log.i("已发送音量设置命令: 值=$deviceVolume");
      return;
    });
  }

  /// 获取音量的百分比值（用于滑块显示）
  double getVolumePercentage(int volume) {
    var range = getDeviceVolumeRange();
    int minVolume = range[0]; // 最小值 (通常是-99)
    int maxVolume = range[1]; // 最大值 (DX5: 0, D900: 8)

    // 确保输入值在有效范围内
    int clampedVolume = volume.clamp(minVolume, maxVolume);

    // 计算百分比
    double percentage =
        (clampedVolume - minVolume) / (maxVolume - minVolume) * 100.0;
    Log.d("计算音量百分比: 原值=$volume, 限制值=$clampedVolume, 百分比=$percentage%");

    // 最后确保百分比值一定在0-100范围内
    return percentage.clamp(0.0, 100.0);
  }

  /// 从百分比值转换为设备音量值
  int volumeFromPercentage(double percentage) {
    // 确保百分比值在0-100范围内
    double clampedPercentage = percentage.clamp(0.0, 100.0);

    var range = getDeviceVolumeRange();
    int minVolume = range[0]; // 最小值 (通常是-99)
    int maxVolume = range[1]; // 最大值 (DX5: 0, D900: 8)

    // 计算音量值 (线性映射)
    int volume =
        (minVolume + (clampedPercentage / 100.0) * (maxVolume - minVolume))
            .round();

    // 由于四舍五入可能导致超出范围，再次限制
    volume = volume.clamp(minVolume, maxVolume);

    Log.d("从百分比计算音量: 百分比=$clampedPercentage%, 音量=$volume dB");
    return volume;
  }

  /// 为UI显示获取格式化的音量值
  String getFormattedVolumeText(int volume) {
    Log.i("【音量调试】getFormattedVolumeText 输入值: $volume");
    var range = getDeviceVolumeRange();
    // 确保音量在有效范围内
    int clampedVolume = volume.clamp(range[0], range[1]);
    Log.i("【音量调试】getFormattedVolumeText 限制后的值: $clampedVolume, 范围: $range");

    // 格式化显示，正值显示加号
    String result;
    if (clampedVolume > 0) {
      result = "+$clampedVolume dB";
    } else {
      result = "$clampedVolume dB";
    }
    Log.i("【音量调试】getFormattedVolumeText 最终结果: $result");
    return result;
  }

  /// 切换静音状态
  void toggleMuteState(bool newMuteState) {
    // 直接调用设备管理器方法
    _executeDeviceAction("设置静音为 $newMuteState", (manager) async {
      manager.setMute(newMuteState);
      return;
    });
  }

  /// 设置输入 (示例，假设 UI 传递的是枚举值)
  void setInput(dynamic inputEnumValue) {
    if (inputEnumValue != null) {
      int inputValue = inputEnumValue.index;
      // 直接调用设备管理器方法
      _executeDeviceAction("设置输入为 $inputValue", (manager) async {
        manager.setInputType(inputValue);
        return;
      });
    }
  }

  /// 设置输出 (示例)
  void setOutput(dynamic outputEnumValue) {
    if (outputEnumValue != null) {
      int outputValue = outputEnumValue.index;
      // 直接调用设备管理器方法
      _executeDeviceAction("设置输出为 $outputValue", (manager) async {
        manager.setOutputType(outputValue);
        return;
      });
    }
  }

  /// 切换耳机启用 (示例)
  void toggleHeadphone(bool enable) {
    // 直接调用设备管理器方法
    _executeDeviceAction("${enable ? '启用' : '禁用'}耳机", (manager) async {
      manager.enableHeadphone(enable);
      return;
    });
  }

  /// 设置耳机增益 (示例)
  void setHeadphoneGain(dynamic gainEnumValue) {
    if (gainEnumValue != null) {
      int gainValue = gainEnumValue.index;
      // 直接调用设备管理器方法
      _executeDeviceAction("设置耳机增益为 $gainValue", (manager) async {
        manager.setHeadphoneGain(gainValue);
        return;
      });
    }
  }

  /// 设置显示模式 (示例)
  void setDisplayMode(dynamic displayEnumValue) {
    if (displayEnumValue != null) {
      int displayModeValue = displayEnumValue.index;
      // 直接调用设备管理器方法
      _executeDeviceAction("设置显示模式为 $displayModeValue", (manager) async {
        manager.setDisplayMode(displayModeValue);
        return;
      });
    }
  }

  // --- 同步和加载 ---

  // 同步设备设置 (依赖DeviceSyncUtil中的设置请求，只启动事件同步器)
  void _syncDeviceSettings() {
    Log.i('设备已连接，依赖DeviceSyncUtil中的设置请求，只启动事件同步器');
    // 不再请求设置，只启动事件同步器监听设置变化
  }

  /// 初始化PEQ相关依赖
  void initPEQDependencies() {
    if (state.deviceId.isEmpty) return;

    if (Get.isRegistered<PEQController>()) {
      Get.delete<PEQController>();
    }

    // 使用PEQBindings注册所有PEQ相关依赖，传入deviceId
    final peqBindings = PEQBindings(state.deviceId);
    peqBindings.dependencies();
  }

  /// 初始化背景设置
  void _initBackgroundSettings() {
    state.backgroundImage.value = backgroundManager.currentBackground.value;
    state.backgroundOpacity.value = backgroundManager.backgroundOpacity.value;
    state.backgroundBlur.value = backgroundManager.backgroundBlur.value;

    // Listen for changes
    ever(backgroundManager.currentBackground, (path) {
      state.backgroundImage.value = path;
    });
    ever(backgroundManager.backgroundOpacity, (opacity) {
      state.backgroundOpacity.value = opacity;
    });
    ever(backgroundManager.backgroundBlur, (blur) {
      state.backgroundBlur.value = blur;
    });
  }

  /// 加载设备设置
  Future<void> _loadDeviceSettings() async {
    if (state.deviceId.isEmpty) return;

    try {
      final device = storage.getDevice(state.deviceId);
      final savedSettings = storage.getDeviceSettings(state.deviceId);

      if (savedSettings != null) {
        state.settings.value = savedSettings;
        if (savedSettings is Dx5DeviceSettings) {
          Log.i('从本地存储加载 DX5 设备设置成功');
          // 初始化DX5设备的可用选项
          _initDx5AvailableOptions();
        } else if (savedSettings is D900DeviceSettings) {
          Log.i("从本地存储加载 D900 设备设置成功");
          // 初始化D900设备的可用选项
          _initD900AvailableOptions();
        } else {
          Log.e("未知的设置类型: ${savedSettings.runtimeType}");
        }
      } else if (device != null) {
        Log.i('未找到设备设置，根据设备型号创建默认设置: ${device.deviceModel}');
        final settings =
            DeviceSettingsFactory.createDefaultSettings(device.deviceModel);

        if (settings is Dx5DeviceSettings || settings is D900DeviceSettings) {
          if (settings is Dx5DeviceSettings) {
            settings.deviceId = state.deviceId;
            // 初始化DX5设备的可用选项
            _initDx5AvailableOptions();
          } else if (settings is D900DeviceSettings) {
            settings.deviceId = state.deviceId;
            // 初始化D900设备的可用选项
            _initD900AvailableOptions();
          }
          state.settings.value = settings;
          Log.i('创建默认设置成功: ${settings.runtimeType}');
          _saveDeviceSettings(); // 保存新创建的默认设置
        } else {
          Log.e('创建设置失败，未知类型: ${settings?.runtimeType}');
          if (DeviceSettingsFactory.isD900Device(device.deviceModel)) {
            state.settings.value = D900DeviceSettings(deviceId: state.deviceId);
            // 初始化D900设备的可用选项
            _initD900AvailableOptions();
          } else {
            state.settings.value = Dx5DeviceSettings(deviceId: state.deviceId);
            // 初始化DX5设备的可用选项
            _initDx5AvailableOptions();
          }
          _saveDeviceSettings(); // 保存新创建的默认设置
        }
      } else {
        Log.e('未找到设备和设置');
        if (state.device.value != null &&
            DeviceSettingsFactory.isD900Device(
                state.device.value!.deviceModel)) {
          state.settings.value = D900DeviceSettings(deviceId: state.deviceId);
          // 初始化D900设备的可用选项
          _initD900AvailableOptions();
        } else {
          state.settings.value = Dx5DeviceSettings(deviceId: state.deviceId);
          // 初始化DX5设备的可用选项
          _initDx5AvailableOptions();
        }
        _saveDeviceSettings(); // 保存新创建的默认设置
      }
    } catch (e) {
      Log.e('设置加载失败: $e');
      if (state.device.value != null &&
          DeviceSettingsFactory.isD900Device(state.device.value!.deviceModel)) {
        state.settings.value = D900DeviceSettings(deviceId: state.deviceId);
        // 初始化D900设备的可用选项
        _initD900AvailableOptions();
      } else {
        state.settings.value = Dx5DeviceSettings(deviceId: state.deviceId);
        // 初始化DX5设备的可用选项
        _initDx5AvailableOptions();
      }
      _saveDeviceSettings(); // 保存新创建的默认设置
    }
  }

  /// 保存设备设置
  Future<void> _saveDeviceSettings() async {
    if (state.deviceId.isEmpty || state.currentSettings == null) return;
    try {
      storage.saveDeviceSettings(
          state.deviceId, state.currentSettings!); // 使用 currentSettings 获取最新值
      Log.d("Device settings saved manually (e.g., after creation).");
    } catch (e) {
      Log.e('设置手动保存失败: $e');
    }
  }

  /// 初始化DX5设备的可用选项
  void _initDx5AvailableOptions() {
    // 初始化输入选项
    state.availableInputs.clear();
    state.availableInputs.addAll(Dx5InputType.values);
    Log.i('初始化DX5输入选项: ${state.availableInputs.length}个');

    // 初始化输出选项
    state.availableOutputs.clear();
    state.availableOutputs.addAll(Dx5OutputType.values);
    Log.i('初始化DX5输出选项: ${state.availableOutputs.length}个');

    // 初始化显示模式选项
    state.availableDisplayModes.clear();
    state.availableDisplayModes.addAll(Dx5DisplayType.values);
    Log.i('初始化DX5显示模式选项: ${state.availableDisplayModes.length}个');
  }

  /// 初始化D900设备的可用选项
  void _initD900AvailableOptions() {
    // 初始化输入选项
    state.availableInputs.clear();
    state.availableInputs.addAll(D900InputType.values);
    Log.i('初始化D900输入选项: ${state.availableInputs.length}个');

    // 初始化输出选项
    state.availableOutputs.clear();
    state.availableOutputs.addAll(D900OutputType.values);
    Log.i('初始化D900输出选项: ${state.availableOutputs.length}个');

    // 初始化显示模式选项
    state.availableDisplayModes.clear();
    state.availableDisplayModes.addAll(D900DisplayType.values);
    Log.i('初始化D900显示模式选项: ${state.availableDisplayModes.length}个');
  }

  /// 设置电源状态
  void setPower(bool isOn) {
    // 直接调用设备管理器方法，使用动态类型因为DeviceManager接口没有定义此方法
    _executeDeviceAction("设置电源为 ${isOn ? '开启' : '关闭'}", (manager) async {
      (manager as dynamic).powerOn(isOn);
      return;
    });
  }

  /// 设置语言
  void setLanguage(dynamic language) {
    // 修复类型转换问题，确保可以处理Enum类型
    int langValue = language.index;

    _executeDeviceAction("设置语言为 $langValue", (manager) async {
      (manager as dynamic).setLanguage(langValue);
      return;
    });
  }

  /// 设置开关机触发
  void setPowerTrigger(dynamic trigger) {
    // 修复类型转换问题
    int triggerValue;
    if (trigger is Enum) {
      triggerValue = trigger.index;
    } else if (trigger is int) {
      triggerValue = trigger;
    } else {
      Log.e("设置开关机触发时类型错误: $trigger 不是有效的触发类型");
      return;
    }

    _executeDeviceAction("设置开关机触发为 $triggerValue", (manager) async {
      (manager as dynamic).setPowerTrigger(triggerValue);
      return;
    });
  }

  /// 检查固件更新
  Future<void> checkFirmwareUpdate() async {
    final device = state.device.value;
    if (device == null) {
      Log.w("设备信息缺失，无法检查更新");
      state.hasNewFirmware.value = false;
      return;
    }
    _firmwareService.setDeviceInfo(device.deviceModel, device.macAddress);
    Log.i("开始检查固件更新 (无版本比较)");
    try {
      final hasApiResult = await _firmwareService.checkUpdate();
      if (hasApiResult) {
        Log.i("检查到服务器固件信息");
        state.hasNewFirmware.value = true;
      } else {
        Log.i("未检查到可用固件更新或 API 请求失败");
        state.hasNewFirmware.value = false;
        Get.snackbar(
          l10n.firmwareUpdateTitle,
          l10n.aboutUpdateHint,
          backgroundColor: ColorPalettes.instance.success,
          colorText: ColorPalettes.instance.pure,
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      Log.e("检查固件更新过程中发生异常", e);
      state.hasNewFirmware.value = false;
      Get.snackbar(
        l10n.error,
        l10n.checkUpdateFailed,
        backgroundColor: ColorPalettes.instance.error,
        colorText: ColorPalettes.instance.pure,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// 切换电源状态
  void togglePower() {
    final manager = _deviceManager;
    if (manager != null && state.currentSettings != null) {
      // 获取当前电源状态的相反值 (从 state 读取)
      final bool currentPowerState = state.currentSettings!.power ?? false;
      final bool newPowerState = !currentPowerState;

      // 发送命令
      setPower(newPowerState);
    }
  }

  /// 订阅设置更新流
  void _subscribeToSettingsUpdates() {
    if (state.deviceId.isEmpty) return;

    _settingsSubscription?.cancel();
    _settingsSubscription =
        storage.getSettingsStream(state.deviceId).listen((updatedSettings) {
      Log.d('DeviceDetailLogic: Received settings update from stream');
      if (updatedSettings != null) {
        Log.i('【音量调试】DeviceDetailLogic: 接收到设置更新, 当前音量值: ${state.settings.value?.volume}, 新音量值: ${updatedSettings.volume}');
        Log.d(
            'DeviceDetailLogic: Updating settings with new values，updatedSettings: $updatedSettings');
        // 完全替换对象，而不是修改属性
        state.settings.value = updatedSettings;
        // 显式刷新状态
        state.settings.refresh();
        // 通知 GetBuilder 更新
        update();
        Log.i('【音量调试】DeviceDetailLogic: 设置更新完成, 更新后的音量值: ${state.settings.value?.volume}');
      }
    }, onError: (error) {
      Log.e('DeviceDetailLogic: Error in settings stream: $error');
    });

    Log.i('DeviceDetailLogic: Subscribed to settings updates stream');
  }

  @override
  void onClose() {
    _connectionSubscription?.cancel(); // 取消连接监听
    _stopEventSynchronizer(); // 停止事件同步器
    _settingsSubscription?.cancel(); // 取消设置更新流订阅
    super.onClose();
  }

}
