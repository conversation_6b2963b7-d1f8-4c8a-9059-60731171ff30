import 'package:get/get.dart';
import 'package:topping_home/business/account_binding/account_binding_binding.dart';
import 'package:topping_home/business/account_binding/account_binding_view.dart';
import 'package:topping_home/business/account_security/account_security_binding.dart';
import 'package:topping_home/business/account_security/account_security_view.dart';
import 'package:topping_home/business/agreement/agreement_binding.dart';
import 'package:topping_home/business/cancel_account/cancel_account_binding.dart';
import 'package:topping_home/business/cancel_account/cancel_account_view.dart';
import 'package:topping_home/business/device_setting/device_setting_view.dart';
import 'package:topping_home/business/edit_personal_info/edit_personal_info_binding.dart';
import 'package:topping_home/business/edit_personal_info/edit_personal_info_view.dart';
import 'package:topping_home/business/guide/guide_view.dart';
import 'package:topping_home/business/login/password/password_binding.dart';
import 'package:topping_home/business/modify_mobile/modify_mobile_binding.dart';
import 'package:topping_home/business/password_change/password_change_binding.dart';
import 'package:topping_home/business/password_change/password_change_view.dart';
import 'package:topping_home/business/personal_center/personal_center_binding.dart';
import 'package:topping_home/business/personal_center/personal_center_view.dart';
import 'package:topping_home/business/qr_scan/qr_scan_binding.dart';
import 'package:topping_home/business/qr_scan/qr_scan_view.dart';

import '../business/about/about_binding.dart';
import '../business/about/about_view.dart';
import '../business/agreement/agreement_view.dart';
import '../business/custom_background/custom_background_binding.dart';
import '../business/custom_background/custom_background_view.dart';
import '../business/device_detail/device_detail_binding.dart';
import '../business/device_detail/device_detail_view.dart';
import '../business/device_setting/device_setting_binding.dart';
import '../business/feedback/feedback_binding.dart';
import '../business/feedback/feedback_view.dart';
import '../business/guide/guide_binding.dart';
import '../business/index/index_binding.dart';
import '../business/index/index_view.dart';
import '../business/login/password/password_view.dart';
import '../business/modify_mobile/modify_mobile_view.dart';
import '../business/privacy/privacy_binding.dart';
import '../business/privacy/privacy_view.dart';
import '../business/register/register_binding.dart';
import '../business/register/register_view.dart';
import '../business/setting/setting_binding.dart';
import '../business/setting/setting_view.dart';
import '../business/peq/ui/settings/peq_settings_page.dart';
import '../business/firmware/firmware_update_binding.dart';
import '../business/firmware/firmware_update_view.dart';
import '../repositories/user_repository.dart';

class AppRoutes {
  static RxString curPage = indexPage.obs;
  static final prePage = Rxn<String>();

  static const indexPage = "/home";

  static const verifyCodeLoginPage = "/verifyCodeLoginPage";

  static const passwordLoginPage = "/passwordLoginPage";

  static const aboutPage = "/aboutPage";

  static const quickStartPage = "/quickStartPage";

  static const feedbackPage = "/feedbackPage";

  static const customBackgroundPage = "/customBackgroundPage";

  static const registerPage = "/registerPage";

  static const personalCenterPage = "/personalCenterPage";

  static const editPersonalInfoPage = "/editPersonalInfoPage";

  static const accountSecurityPage = "/accountSecurityPage";

  static const passwordModifyPage = "/passwordModifyPage";

  static const accountBindPage = "/accountBindPage";

  static const modifyPhonePage = "/modifyPhonePage";

  static const cancelAccountPage = "/cancelAccountPage";

  static const deviceAddPage = "/deviceAddPage";

  static const qrCodeScanPage = "/qrCodeScanPage";

  static const operateDescPage = "/operateDescPage";

  static const deviceDetailPage = "/deviceDetailPage";

  static const settingPage = "/settingPage";

  static const deviceSettingPage = "/deviceSettingPage";

  static const privacyPage = "/privacyPage";

  static const agreementPage = "/agreementPage";

  static const peqSettingsPage = "/peq/settings";

  static const firmwareUpdatePage = "/firmwareUpdatePage";

  static const logViewerPage = "/logViewerPage";

  static final routerPages = [
    ///主入口
    GetPage(name: indexPage, page: () => IndexPage(), binding: IndexBinding()),

    /// 密码登录页面
    GetPage(
        name: passwordLoginPage,
        page: () => LoginPasswordPage(),
        binding: LoginPasswordBinding()),

    /// 关于页面
    GetPage(
        name: aboutPage,
        page: () => const AboutPage(),
        binding: AboutBinding()),

    /// 快速开始页面
    GetPage(
      name: quickStartPage,
      page: () => const GuidePage(),
      binding: GuideBinding(),
    ),

    /// 意见反馈页面
    GetPage(
        name: feedbackPage,
        page: () => const FeedbackPage(),
        binding: FeedbackBinding()),

    /// 自定义背景页面
    GetPage(
        name: customBackgroundPage,
        page: () => const CustomBackgroundPage(),
        binding: CustomBackgroundBinding()),

    /// 注册页面
    GetPage(
        name: registerPage,
        page: () => const RegisterPage(),
        binding: RegisterBinding()),

    /// 个人中心页面
    GetPage(
        name: personalCenterPage,
        page: () => PersonalCenterPage(),
        binding: PersonalCenterBinding()),

    /// 编辑个人信息页面
    GetPage(
        name: editPersonalInfoPage,
        page: () => EditPersonalInfoPage(),
        binding: EditPersonalInfoBinding()),

    /// 账号安全设置页面
    GetPage(
        name: accountSecurityPage,
        page: () => AccountSecurityPage(),
        binding: AccountSecurityBinding()),

    /// 登录密码修改
    GetPage(
        name: passwordModifyPage,
        page: () => PasswordChangePage(),
        binding: PasswordChangeBinding()),

    /// 账号与绑定设置
    GetPage(
        name: accountBindPage,
        page: () => AccountBindingPage(),
        binding: AccountBindingBinding()),

    /// 修改手机号
    GetPage(
        name: modifyPhonePage,
        page: () => ModifyMobilePage(),
        binding: ModifyMobileBinding()),

    /// 注销账号
    GetPage(
        name: cancelAccountPage,
        page: () => CancelAccountPage(),
        binding: CancelAccountBinding()),

    /// 扫码页面
    GetPage(
        name: qrCodeScanPage,
        page: () => QRScanPage(),
        binding: QRScanBinding()),

    /// 设备详情
    GetPage(
        name: deviceDetailPage,
        page: () => DeviceDetailPage(),
        binding: DeviceDetailBinding()),

    /// 设置页面
    GetPage(
        name: settingPage,
        page: () => SettingPage(),
        binding: SettingBinding()),

    /// 设备设置页面
    GetPage(
        name: deviceSettingPage,
        page: () => DeviceSettingPage(),
        binding: DeviceSettingBinding()),

    /// 隐私页面
    GetPage(
        name: privacyPage,
        page: () => const PrivacyPage(),
        binding: PrivacyBinding()),

    /// 协议页面
    GetPage(
        name: agreementPage,
        page: () => const AgreementPage(),
        binding: AgreementBinding()),

    /// PEQ设置页面
    GetPage(
      name: peqSettingsPage,
      page: () => PEQSettingsPage(controller: Get.arguments),
    ),

    /// 固件升级页面
    GetPage(
      name: firmwareUpdatePage,
      page: () => const FirmwareUpdatePage(),
      binding: FirmwareUpdateBinding(),
    ),
  ];

  static Future<T?>? jumpPage<T>(String page,
      {dynamic arguments,
      int? id,
      bool preventDuplicates = true,
      Map<String, String>? parameters,
      bool needLogin = false,
      String? tag}) {
    if (needLogin && !UserRepository.instance.isLogin()) {
      return Get.toNamed(verifyCodeLoginPage);
    } else {
      tag = tag ?? DateTime.now().millisecondsSinceEpoch.toString();
      if (page == passwordLoginPage) {
        return Get.toNamed(
          passwordLoginPage,
          arguments: arguments,
          preventDuplicates: false,
        );
      } else if (page == aboutPage) {
        return Get.toNamed(
          aboutPage,
          arguments: arguments,
          preventDuplicates: false,
        );
      } else if (page == quickStartPage) {
        return Get.toNamed(
          quickStartPage,
          arguments: arguments,
          preventDuplicates: false,
        );
      } else if (page == feedbackPage) {
        return Get.toNamed(
          feedbackPage,
          arguments: arguments,
          preventDuplicates: false,
        );
      } else if (page == customBackgroundPage) {
        return Get.toNamed(
          customBackgroundPage,
          arguments: arguments,
          preventDuplicates: false,
        );
      } else if (page == registerPage) {
        return Get.toNamed(
          registerPage,
          arguments: arguments,
          preventDuplicates: false,
        );
      } else if (page == personalCenterPage) {
        return Get.toNamed(
          personalCenterPage,
          arguments: arguments,
          preventDuplicates: false,
        );
      } else if (page == editPersonalInfoPage) {
        return Get.toNamed(editPersonalInfoPage,
            arguments: arguments, preventDuplicates: false);
      } else if (page == accountSecurityPage) {
        return Get.toNamed(accountSecurityPage,
            arguments: arguments, preventDuplicates: false);
      } else if (page == passwordModifyPage) {
        return Get.toNamed(passwordModifyPage,
            arguments: arguments, preventDuplicates: false);
      } else if (page == accountBindPage) {
        return Get.toNamed(accountBindPage,
            arguments: arguments, preventDuplicates: false);
      } else if (page == modifyPhonePage) {
        return Get.toNamed(modifyPhonePage,
            arguments: arguments, preventDuplicates: false);
      } else if (page == cancelAccountPage) {
        return Get.toNamed(cancelAccountPage,
            arguments: arguments, preventDuplicates: false);
      } else if (page == deviceAddPage) {
        return Get.toNamed(deviceAddPage,
            arguments: arguments, preventDuplicates: false);
      } else if (page == qrCodeScanPage) {
        return Get.toNamed(qrCodeScanPage,
            arguments: arguments, preventDuplicates: false);
      } else if (page == operateDescPage) {
        return Get.toNamed(operateDescPage,
            arguments: arguments, preventDuplicates: false);
      } else if (page == deviceDetailPage) {
        return Get.toNamed(deviceDetailPage,
            arguments: arguments, preventDuplicates: false);
      } else if (page == settingPage) {
        return Get.toNamed(settingPage,
            arguments: arguments, preventDuplicates: false);
      } else if (page == privacyPage) {
        return Get.toNamed(privacyPage,
            arguments: arguments, preventDuplicates: false);
      } else if (page == agreementPage) {
        return Get.toNamed(agreementPage,
            arguments: arguments, preventDuplicates: false);
      } else if (page == peqSettingsPage) {
        return Get.toNamed(peqSettingsPage,
            arguments: arguments, preventDuplicates: false);
      } else if (page == firmwareUpdatePage) {
        return Get.toNamed(firmwareUpdatePage,
            arguments: arguments, preventDuplicates: false);
      } else {
        return null;
      }
    }
  }
}
