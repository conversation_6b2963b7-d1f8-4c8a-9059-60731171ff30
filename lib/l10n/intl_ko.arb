{"@@locale": "ko", "appName": "토핀 홈", "homeTitle": "내 제품", "homeAddHint": "제품을 추가하여 더 많은 기능을 이용해보세요", "addDevice": "기기 추가", "noDevice": "기기 없음", "addDeviceHint": "제품을 추가하여 더 나은 사용 경험을 얻으세요", "foundDevice": "기기 발견", "devices": "기기", "loginClick": "로그인하기", "about": "소개", "quickStart": "빠른 시작", "feedback": "피드백", "customBackground": "배경 사용자 정의", "logout": "로그아웃", "registerTitle": "회원가입", "phone": "핸드폰 번호", "phoneHint": "핸드폰 번호를 입력하세요", "verifyCode": "인증 코드", "getSmsVerifyCode": "SMS 인증 코드 받기", "agreement": "이용약관 및 개인정보처리방침에 동의합니다", "agreementLink": "《이용약관》", "agree": "동의", "privacyPolicy": "《개인정보처리방침》", "register": "회원가입", "login": "로그인", "loginTitle": "로그인", "loginPhoneHint": "핸드폰 번호를 입력하세요", "loginVerifyCode": "인증 코드", "loginGetSmsVerifyCode": "SMS 인증 코드 받기", "loginPassword": "비밀번호로 로그인", "loginForgetPassword": "비밀번호 찾기", "loginFail": "로그인 실패", "passwordError": "비밀번호 오류", "phoneNotRegistered": "등록되지 않은 핸드폰 번호", "noAccount": "계정이 없으신가요?", "registerNow": "지금 가입하기", "tips": "알림", "graphicCodeHint": "그래픽 인증 코드를 입력하세요", "agreeToTermsHint": "이용약관 및 개인정보처리방침에 동의해주세요", "verificationCodeHint": "인증 코드를 입력하세요", "usernameHint": "사용자 이름을 입력하세요", "passwordHint": "비밀번호를 입력하세요", "confirmPasswordHint": "비밀번호를 다시 입력하세요", "passwordMismatch": "비밀번호가 일치하지 않습니다", "registerSuccess": "회원가입 성공", "registerSuccessHint": "핸드폰 번호와 비밀번호로 로그인하세요", "registerFailed": "회원가입 실패", "getVerificationCode": "SMS 인증 코드 받기", "verificationCodeSent": "인증 코드가 다음으로 전송되었습니다", "next": "다음", "secondResend": "초 후 재전송", "settingAccount": "계정 설정", "registerComplete": "회원가입 완료", "username": "사용자 이름", "password": "비밀번호", "confirmPassword": "비밀번호 확인", "inputError": "입력 오류", "inputCannotBeEmpty": "입력란을 비워둘 수 없습니다", "invalidCaptcha": "인증 코드 오류", "forgetPassword": "비밀번호 찾기", "forgetPasswordTitle": "비밀번호 찾기", "forgetPasswordPhoneTitle": "핸드폰 번호를 입력하세요", "forgetPasswordPhoneHint": "등록된 핸드폰 번호로 인증 코드를 받아 다음 단계를 진행하세요", "forgetPasswordVerifyCode": "인증 코드", "forgetPasswordGetSmsVerifyCode": "SMS 인증 코드 받기", "forgetPasswordNext": "다음", "forgetPasswordNewPassword": "새 비밀번호", "forgetPasswordNewPasswordHint": "새 비밀번호를 입력하세요", "forgetPasswordConfirmPassword": "비밀번호 확인", "forgetPasswordConfirmPasswordHint": "새 비밀번호를 다시 입력하세요", "forgetPasswordReset": "비밀번호 재설정", "forgetPasswordSuccess": "비밀번호 재설정 성공", "forgetPasswordSuccessHint": "새 비밀번호로 로그인하세요", "forgetPasswordSuccessBack": "로그인으로 돌아가기", "forgetPasswordSuccessBackHome": "홈으로 돌아가기", "forgetPasswordSuccessBackLogin": "로그인으로 돌아가기", "forgetPasswordSuccessBackRegister": "회원가입으로 돌아가기", "forgetPasswordSuccessBackForgetPassword": "비밀번호 찾기로 돌아가기", "aboutTitle": "소개", "aboutVersion": "버전", "aboutVersionHint": "1.0.0", "aboutUpdate": "업데이트 확인", "aboutUpdateHint": "최신 버전입니다", "aboutUpdateSuccess": "업데이트 성공", "feedbackTitle": "피드백", "feedbackHint": "귀중한 의견을 남겨주시면 제품을 지속적으로 개선하겠습니다", "feedbackContact": "연락처", "feedbackContactHint": "연락처를 입력하세요", "feedbackContent": "피드백 내용", "feedbackContentHint": "피드백 내용을 입력하세요", "feedbackImage": "이미지 업로드", "feedbackImageHint": "최대 5장까지 업로드 가능합니다", "feedbackDevice": "기기 모델", "feedbackDeviceHint": "기기 모델을 선택하세요", "feedbackSubmit": "제출", "feedbackSuccess": "제출 성공", "feedbackSuccessHint": "피드백 감사합니다. 빠른 시일 내에 처리하겠습니다", "feedbackType": "피드백 유형", "feedbackTypeRequired": "피드백 유형을 선택하세요", "feedbackError": "제출 실패, 나중에 다시 시도하세요", "error": "오류", "logoutTitle": "로그아웃", "logoutHint": "로그아웃하시겠습니까?", "cancel": "취소", "confirm": "확인", "deviceNotFoundTitle": "기기를 찾을 수 없습니까?", "deviceNotFoundHint": "1. 기기가 켜져 있는지 확인하세요\n2. 기기가 휴대폰과 블루투스로 연결되어 있는지 확인하세요\n3. 위 단계를 완료하고 새로고침 버튼을 눌러 다시 검색하세요\n4. 일부 휴대폰 시스템에서는 블루투스 BLE 연결을 위해 위치 서비스를 활성화해야 할 수 있습니다. 위치 서비스를 활성화하고 다시 새로고침을 시도해보세요", "operateDescription": "작동 설명", "blueHeadAmp": "블루투스 헤드폰 앰프", "decoderAmp": "디코더 앰프", "headset": "헤드폰", "bluetoothHeadphoneAmplifier": "블루투스 및 헤드폰", "player": "플레이어", "inputLinker": "Linker 컨트롤 IP 수동 입력", "connect": "연결됨", "disconnected": "연결 해제됨", "connecting": "연결 중", "disconnecting": "연결 해제 중", "disconnect": "연결되지 않음", "personalCenter": "개인 센터", "editPersonalInfo": "개인 정보 편집", "accountSecurity": "계정 보안 설정", "avatar": "프로필 사진", "gender": "성별", "secret": "비공개", "male": "남성", "female": "여성", "birthday": "생일", "signature": "상태 메시지", "region": "지역", "nickname": "닉네임", "save": "저장", "success": "성공", "updateSuccess": "업데이트 성공", "userNotFound": "사용자를 찾을 수 없습니다", "loginPasswordModify": "로그인 비밀번호 변경", "accountAndBinding": "계정 및 연동 설정", "modifyPhone": "핸드폰 번호 변경", "cancelAccount": "계정 삭제", "modifyPassword": "비밀번호 변경", "oldPassword": "기존 비밀번호", "newPassword": "새 비밀번호", "confirmNewPassword": "새 비밀번호 확인", "modifyPasswordSuccess": "비밀번호 변경 성공", "informationIncomplete": "정보가 불완전합니다", "oldPasswordError": "기존 비밀번호 오류", "newPasswordAndConfirmPasswordNotConsistent": "새 비밀번호와 확인 비밀번호가 일치하지 않습니다", "oldPasswordAndNewPasswordCannotBeTheSame": "기존 비밀번호와 새 비밀번호는 같을 수 없습니다", "newPasswordCannotBeTheSameAsTheOldPassword": "새 비밀번호는 기존 비밀번호와 같을 수 없습니다", "emailBinding": "이메일 연동", "notBound": "연동되지 않음", "bound": "연동됨", "nowEmailVerification": "이메일 인증하기", "emailVerificationPrompt": "연동하려는 이메일을 입력하세요. 이후 이메일 변경 및 비밀번호 찾기에 사용할 수 있습니다", "email": "이메일", "sendVerificationCode": "인증 코드 전송", "smsVerificationCode": "SMS 인증 코드", "verificationCodeHasBeenSent": "인증 코드가 전송되었습니다", "accountBindingSuccess": "계정 연동 성공", "emailEmpty": "이메일을 입력하세요", "invalidEmail": "이메일 형식이 올바르지 않습니다", "emailBindSuccess": "이메일 연동 성공", "unbindEmail": "이메일 연동 해제", "unbindEmailConfirm": "이메일 연동을 해제하시겠습니까?", "emailUnbindSuccess": "이메일 연동 해제 성공", "boundEmail": "연동된 이메일", "unbind": "연동 해제", "bind": "연동하기", "getEmailVerificationCode": "이메일 인증 코드 받기", "bindNewPhone": "새 핸드폰 번호 연동", "bindNewPhonePrompt": "새 핸드폰 번호를 입력하고 인증 코드를 받아 확인하세요", "bindNewPhoneSuccess": "새 핸드폰 번호 연동 성공", "bindNewPhoneFailure": "새 핸드폰 번호 연동 실패", "bindNewPhoneFailurePrompt": "새 핸드폰 번호 연동 실패, 나중에 다시 시도하세요", "will": "을(를)", "accountWillBeCancelled": "연동된 계정 삭제", "cancellationInstructions": "주의: 계정 삭제는 본인만 할 수 있으며, 삭제 후에는 복구할 수 없습니다. 신중하게 결정하세요.\n계정 삭제 후에는 이 계정을 더 이상 사용할 수 없으며, 계정과 관련된 모든 정보를 복구할 수 없습니다. 다음 항목이 포함됩니다:\n\n1. 이 계정의 개인 정보(프로필 사진, 닉네임, 연결된 제품 등)\n2. 이 계정의 다양한 권한(MQA 멤버십, 포럼 포인트 등)\n\n3. 이 계정의 다양한 콘텐츠(재생 목록 등)\n\n4. 계정 삭제로 인해 발생할 수 있는 기타 결과", "cancellation": "삭제", "confirmCancellation": "계정을 삭제하시겠습니까?", "cancellationSuccess": "삭제 성공", "cancellationFailure": "삭제 실패", "exitApp": "앱 종료", "exitAppPrompt": "앱을 종료하시겠습니까?", "phoneUpdateSuccess": "핸드폰 번호 변경 성공", "phoneEmpty": "핸드폰 번호를 입력하세요", "verificationCodeEmpty": "인증 코드를 입력하세요", "samePhoneNumber": "새 핸드폰 번호가 기존 번호와 동일합니다", "verifyOldPhone": "기존 핸드폰 번호 인증", "setNewPhone": "새 핸드폰 번호 설정", "oldPhone": "기존 핸드폰 번호", "newPhone": "새 핸드폰 번호", "passwordTooShort": "비밀번호는 6자 이상이어야 합니다", "passwordEmpty": "비밀번호를 입력하세요", "passwordUpdateSuccess": "비밀번호 업데이트 성공", "passwordUpdateSuccessRelogin": "비밀번호 변경 성공, 다시 로그인하세요", "scanning": "스캔 중", "scanningDevicesHint": "근처의 블루투스 기기를 검색하고 있습니다...", "startScanningHint": "스캔 시작", "manualInputIP": "IP 수동 입력", "manualInputIPHint": "Linker 컨트롤 IP를 입력하세요", "bluetoothAndDac": "블루투스 및 DAC", "streamer": "스트리머", "pleaseInputIP": "IP를 입력하세요", "deviceNotFoundHint1": "1. 기기가 켜져 있는지 확인하세요.", "deviceNotFoundHint2": "2. 기기가 휴대폰과 블루투스로 연결되어 있는지 확인하세요.", "deviceNotFoundHint3": "3. 위 단계를 완료하고 새로고침 버튼을 눌러 다시 검색하세요.", "deviceNotFoundHint4": "4. 일부 휴대폰 시스템에서는 블루투스 BLE 연결을 위해 위치 서비스를 활성화해야 할 수 있습니다. 위치 서비스를 활성화하고 다시 새로고침을 시도해보세요.", "invalidQRCode": "유효하지 않은 QR 코드", "scanQRCode": "QR 코드 스캔", "scanQRCodeHint": "기기의 QR 코드를 스캔하세요", "scanQRCodeBottomHint": "QR 코드를 프레임 안에 놓으면 자동으로 스캔됩니다", "noDevicesFound": "기기를 찾을 수 없습니다", "discoveredDevices": "연결 가능한 제품", "discoveredDevicesHint": "여러 개의 연결 가능한 제품이 발견되었습니다", "tapToConnect": "탭하여 연결", "availableDevices": "사용 가능한 기기", "languageCode": "ko", "deviceNotFound": "기기를 찾을 수 없습니다", "connected": "연결됨", "battery": "배터리", "tryAgain": "다시 시도", "volumeControl": "볼륨 조절", "unlock": "잠금 해제", "lock": "잠금", "audioSettings": "오디오 설정", "displayView": "디스플레이 보기", "low": "낮음", "middle": "중간", "high": "높음", "equalizer": "이퀄라이저", "eqPreset": "EQ 프리셋", "digitalFilter": "디지털 필터", "channelBalance": "채널 밸런스", "dsdMode": "DSD 모드", "systemSettings": "시스템 설정", "displayBrightness": "디스플레이 밝기", "displayTimeout": "디스플레이 타임아웃", "autoPowerOff": "자동 전원 끄기", "disabled": "비활성화", "ledIndicator": "LED 표시등", "connectToAccessSettings": "설정에 접근하기 위해 연결", "connectionInfo": "연결 정보", "codec": "코덱", "deviceInfo": "기기 정보", "firmwareVersion": "펌웨어 버전", "serialNumber": "시리얼 번호", "totalPlayTime": "총 재생 시간", "deleteDevice": "기기 삭제", "deleteDeviceConfirm": "기기를 삭제하시겠습니까?", "delete": "삭제", "preamplifier": "프리앰프", "decodeModeDac": "DAC", "displayVu": "VU 미터", "displayFft": "스펙트럼", "displayNormal": "일반", "inputUsb": "USB", "inputOptical": "광학", "inputCoaxial": "동축", "inputBluetooth": "블루투스", "languageZh": "중국어", "languageEn": "영어", "headphoneGainHigh": "고이득", "headphoneGainLow": "저이득", "multiFunctionKeyInputSelect": "입력 선택", "multiFunctionKeyLineOutSelect": "라인 출력 선택", "multiFunctionKeyHeadphoneOutSelect": "헤드폰 출력 선택", "multiFunctionKeyHomeSelect": "홈 선택", "multiFunctionKeyBrightnessSelect": "밝기 선택", "multiFunctionKeySleep": "화면 끄기", "multiFunctionKeyPcmFilterSelect": "PCM 필터 선택", "multiFunctionKeyMute": "음소거", "multiFunctionKeyPeqSelect": "PEQ 선택", "outputClose": "닫기", "outputSingleEnded": "RCA", "outputBalanced": "XLR", "outputSingleEndedAndBalanced": "RCA+XLR", "powerTriggerSignal": "신호", "powerTriggerVoltage": "12V", "powerTriggerClose": "닫기", "screenBrightnessHigh": "높음", "screenBrightnessMedium": "중간", "screenBrightnessLow": "낮음", "screenBrightnessAuto": "자동", "themeAurora": "오로라", "themeOrange": "오렌지", "themePeru": "페루", "themeGreen": "녹색", "themeKhaki": "카키", "themeRose": "로즈", "themeBlue": "블루", "themePurple": "퍼플", "themeWhite": "화이트", "darkMode": "다크 모드", "lightMode": "라이트 모드", "darkModeDescription": "현재 다크 테마 사용 중", "lightModeDescription": "현재 라이트 테마 사용 중", "usbTypeUac2": "UAC 2.0", "usbTypeUac1": "UAC 1.0", "deviceTypeBluetooth": "블루투스", "deviceTypeDac": "DAC", "deviceTypeHeadphone": "헤드폰 앰프", "deviceTypePlayer": "플레이어", "name": "이름", "type": "유형", "volume": "볼륨", "headphoneOutput": "헤드폰 출력", "headphoneGain": "헤드폰 이득", "inputSelect": "입력 선택", "outputSelect": "출력 선택", "advanced": "고급", "advancedSettings": "고급 설정", "editName": "이름 편집", "enterNewName": "새 이름을 입력하세요", "setting": "설정", "peq": "이퀄라이저", "guide": "사용 안내", "theme": "테마", "powerTrigger": "전원 트리거", "audioBalance": "오디오 밸런스", "filter": "필터", "decodeMode": "디코드 모드", "audioMonitoring": "오디오 블루투스", "bluetoothAptx": "블루투스 APTX", "relay": "리모컨", "multiFunctionKey": "다기능 키 사용자 정의", "usbMode": "USB", "screenBrightness": "화면 밝기", "language": "언어", "resetSettings": "고급 설정 초기화", "restoreFactorySettings": "공장 설정 복원", "channel": "채널", "resetSettingsConfirmation": "고급 설정을 초기화하시겠습니까?", "restoreFactorySettingsConfirmation": "공장 설정으로 복원하시겠습니까?", "import": "가져오기", "export": "내보내기", "target": "목표", "sourceFR": "소스 FR", "eachFilter": "각 필터", "combinedFilter": "결합 필터", "filteredFR": "필터링된 주파수 응답", "raw": "원본", "compensated": "보정됨", "preAmplification": "프리앰프", "peakingFilter": "피킹 필터", "lowPassFilter": "로우패스 필터", "highPassFilter": "하이패스 필터", "lowShelfFilter": "로우쉘프 필터", "highShelfFilter": "하이쉘프 필터", "centerFrequency": "중심 주파수", "connerFrequency": "코너 주파수", "frequency": "주파수", "q": "Q 값", "gain": "게인", "addFilter": "필터 추가", "mode": "모드", "import_target": "목표 곡선 가져오기", "import_sourceFR": "원본 주파수 응답 곡선 가져오기", "selectDataFile": "데이터 파일 선택", "problemType": "문제 유형", "selectDeviceType": "디바이스 유형 선택", "device": "디바이스", "addPicture": "사진 추가", "contact": "연락처", "feedbackTypeFeatureSuggestion": "기능 제안", "feedbackTypeBugReport": "버그 보고", "feedbackTypeUIImprovement": "UI 개선", "feedbackTypeOther": "기타", "checkUpdate": "업데이트 확인", "checking": "확인 중...", "alreadyLatestVersion": "최신 버전입니다", "checkUpdateFailed": "업데이트 확인 실패", "foundNewVersion": "새 버전 발견", "currentVersion": "현재 버전", "newVersion": "새 버전", "updateContent": "업데이트 내용", "later": "나중에", "updateNow": "지금 업데이트", "downloading": "다운로드 중...", "downloadCompleted": "다운로드 완료", "downloadFailed": "다운로드 실패", "storagePermissionDenied": "저장소 권한 거부", "cannotGetDownloadPath": "다운로드 경로를 가져올 수 없음", "installPermissionDenied": "설치 권한 거부", "failedToLoadContent": "콘텐츠 로드 실패", "lastUpdated": "마지막 업데이트", "termsAndConditions": "이용약관", "agreementDialogContent": "이 앱을 사용하려면", "userAgreement": "《이용약관》", "privacyPolicyText": "개인정보처리방침", "disagree": "동의하지 않음", "exportSuccess": "내보내기 성공", "fileSavedTo": "파일 저장 위치:", "fileContentPreview": "파일 내용 미리보기:", "ok": "확인", "openFileLocation": "파일 위치 열기", "sharedFilterFile": "공유된 필터 파일", "sharedFile": "공유된 파일", "cannotOpenFileOrItsDirectory": "파일 또는 해당 디렉토리를 열 수 없습니다", "errorOpeningFileLocation": "파일 위치를 여는 중 오류 발생:", "errorSharingFile": "파일 공유 중 오류 발생", "cannotParseSelectedFile": "선택한 파일을 구문 분석할 수 없습니다. 유효한 CSV 형식이며 주파수 및 응답 데이터가 포함되어 있는지 확인하십시오.", "cannotExtractValidFrequencyAndResponseDataFromCSV": "CSV에서 유효한 주파수 및 응답 데이터를 추출할 수 없습니다.", "importSuccess": "가져오기 성공", "successfullyImportedFile": "파일을 성공적으로 가져왔습니다", "errorImportingFile": "파일 가져오기 중 오류 발생", "noDataToExport": "내보낼 데이터가 없습니다.", "fileSavedToAppFolder": "파일이 앱 폴더에 저장됨:", "errorSavingFile": "파일 저장 중 오류 발생:", "saveMergedFilterFile": "병합된 필터 파일 저장", "startUsing": "사용 시작", "nextPage": "다음 페이지", "background": "배경", "confirmDelete": "삭제 확인", "confirmDeleteHint": "이 배경 이미지를 삭제하시겠습니까?", "opacity": "불투명도", "blur": "흐림 효과", "selectFromAlbum": "앨범에서 선택", "takePhoto": "사진 촬영", "addBackground": "배경 추가", "deleteBackgroundFailed": "배경 삭제 실패", "saveBackgroundFailed": "배경 저장 실패", "errorExportingFile": "파일 내보내기 중 오류 발생", "skip": "건너뛰기", "version": "버전", "copyright": " 2025 Topping 테크놀로지 판권소유", "newFirmwareAvailable": "새 펌웨어 사용 가능", "tip": "팁", "firmwareUpgrade": "펌웨어 업그레이드", "firmwareUpgrading": "펌웨어 업그레이드 중", "firmwareUpgradeSuccess": "펌웨어 업그레이드 성공", "firmwareUpgradeFailed": "펌웨어 업그레이드 실패", "firmwareUpgradeConfirm": "펌웨어를 업그레이드하시겠습니까?", "firmwareUpgradeWarning": "업그레이드 중에는 장치 연결을 해제하지 마십시오", "firmwareSize": "펌웨어 크기", "firmwareDescription": "설명", "firmwareForceUpdate": "강제 업데이트", "firmwareDownloading": "펌웨어 다운로드 중", "firmwareInstalling": "펌웨어 설치 중", "settingResetFail": "리셋 실패", "@settingResetFail": {}, "firmwareUpdateTitle": "펌웨어 업데이트", "@firmwareUpdateTitle": {"description": "Title for the firmware update page"}, "firmwareUpdateNewFirmwareFound": "새 펌웨어 발견", "@firmwareUpdateNewFirmwareFound": {}, "firmwareUpdateFirmwareName": "펌웨어 이름", "@firmwareUpdateFirmwareName": {}, "firmwareUpdateVersion": "버전", "@firmwareUpdateVersion": {}, "firmwareUpdateDeviceModel": "기기 모델", "@firmwareUpdateDeviceModel": {}, "firmwareUpdateFileSize": "파일 크기", "@firmwareUpdateFileSize": {}, "firmwareUpdateDescription": "업데이트 노트", "@firmwareUpdateDescription": {}, "firmwareUpdateMandatoryUpdateNote": "* 이 버전은 필수 업데이트입니다", "@firmwareUpdateMandatoryUpdateNote": {}, "firmwareUpdateStatus": "업데이트 상태", "@firmwareUpdateStatus": {}, "firmwareUpdateDownloading": "다운로드 중", "@firmwareUpdateDownloading": {}, "firmwareUpdateUpgrading": "업그레이드 중", "@firmwareUpdateUpgrading": {}, "firmwareUpdateDoNotDisconnect": "연결을 끊거나 기기의 전원을 끄지 마십시오", "@firmwareUpdateDoNotDisconnect": {}, "firmwareUpdateReadyToUpdate": "업데이트 준비 완료", "@firmwareUpdateReadyToUpdate": {}, "firmwareUpdateCancel": "업데이트 취소", "@firmwareUpdateCancel": {}, "firmwareUpdateStart": "업데이트 시작", "@firmwareUpdateStart": {}, "firmwareUpdateLatestVersion": "이미 최신 버전입니다", "@firmwareUpdateLatestVersion": {}, "firmwareUpdateNoNeed": "펌웨어 업데이트가 필요하지 않습니다", "@firmwareUpdateNoNeed": {}, "firmwareUpdateBack": "뒤로", "@firmwareUpdateBack": {}, "firmwareUpdateSuccessTitle": "업데이트 성공", "@firmwareUpdateSuccessTitle": {}, "firmwareUpdateSuccessMessage": "기기 펌웨어가 최신 버전으로 업데이트되었습니다", "@firmwareUpdateSuccessMessage": {}, "firmwareUpdateSuccessRebootMessage": "업그레이드 성공, 기기가 재부팅 중입니다...", "@firmwareUpdateSuccessRebootMessage": {"description": "Message shown when firmware update succeeds and the device disconnects (likely rebooting)"}, "firmwareUpdateDownloadingTitle": "다운로드 중", "@firmwareUpdateDownloadingTitle": {}, "firmwareUpdateDownloadingMessage": "펌웨어 파일을 다운로드 중입니다...", "@firmwareUpdateDownloadingMessage": {}, "firmwareUpdateErrorTitle": "오류", "@firmwareUpdateErrorTitle": {}, "firmwareUpdateDownloadFailed": "펌웨어 다운로드 실패", "@firmwareUpdateDownloadFailed": {}, "firmwareUpdateUpgradingTitle": "업그레이드 중", "@firmwareUpdateUpgradingTitle": {}, "firmwareUpdateUpgradingMessage": "펌웨어 업그레이드를 시작 중입니다...", "@firmwareUpdateUpgradingMessage": {}, "firmwareUpdateSetDeviceFailed": "기기 설정 실패", "@firmwareUpdateSetDeviceFailed": {}, "firmwareUpdateErrorDuringUpdate": "업그레이드 중 오류 발생: {error}", "@firmwareUpdateErrorDuringUpdate": {"placeholders": {"error": {"type": "String", "example": "Connection failed"}}}, "firmwareUpdateCancelledTitle": "취소됨", "@firmwareUpdateCancelledTitle": {}, "firmwareUpdateCancelledMessage": "펌웨어 업그레이드가 취소되었습니다", "@firmwareUpdateCancelledMessage": {}, "commonConfirm": "확인", "@commonConfirm": {}, "checkFirmwareUpdate": "펌웨어 업데이트 확인", "@checkFirmwareUpdate": {"description": "Text for checking for firmware updates"}, "deviceRebootTitle": "기기 재부팅", "@deviceRebootTitle": {"description": "Title for device reboot notification"}, "deviceRebootMessage": "기기가 다시 시작 중입니다. 잠시 기다려 주십시오...", "@deviceRebootMessage": {"description": "Message content for device reboot notification"}, "infoTitle": "정보", "@infoTitle": {"description": "Title for information dialog"}, "errorOpeningFile": "파일 열기 오류: {error}", "@errorOpeningFile": {"placeholders": {"error": {"type": "String", "example": "File not found"}}}, "fileExported": "파일이 내보내기 되었습니다", "@fileExported": {"description": "Message shown when a file has been exported"}, "shareFile": "파일 공유", "@shareFile": {"description": "Button text for sharing a file"}, "peqSettings": "PEQ 설정", "@peqSettings": {"description": "PEQ settings page title"}, "importExport": "가져오기 및 내보내기", "@importExport": {"description": "Import and export section header"}, "configManagement": "구성 관리", "@configManagement": {"description": "Configuration management section header"}, "addConfig": "구성 추가", "@addConfig": {"description": "Add configuration dialog title"}, "configName": "구성 이름", "@configName": {"description": "Configuration name input label"}, "description": "설명 (선택사항)", "@description": {"description": "Description input label"}, "configNameHint": "예: 베이스 부스트, 보컬 강화 등", "@configNameHint": {"description": "Configuration name hint text"}, "configDescriptionHint": "이 구성의 용도를 간략히 설명하세요", "@configDescriptionHint": {"description": "Configuration description hint text"}, "@cancel": {"description": "Cancel button text"}, "add": "추가", "@add": {"description": "Add button text"}, "@save": {"description": "Save button text"}, "@delete": {"description": "Delete button text"}, "addNewConfig": "새 구성 추가", "@addNewConfig": {"description": "Add new configuration tooltip"}, "importTarget": "대상 가져오기", "@importTarget": {"description": "Import target button text"}, "importSourceFR": "소스 FR 가져오기", "@importSourceFR": {"description": "Import source FR button text"}, "exportCombinedFilter": "결합된 필터 내보내기", "@exportCombinedFilter": {"description": "Export combined filter button text"}, "targetFR": "대상 FR", "@targetFR": {"description": "Target frequency response label"}, "@sourceFR": {"description": "Source frequency response label"}, "editConfig": "구성 편집", "@editConfig": {"description": "Edit configuration dialog title/tooltip"}, "deleteConfig": "구성 삭제", "@deleteConfig": {"description": "Delete configuration dialog title/tooltip"}, "deleteConfigConfirmation": "구성 \"{name}\"을(를) 삭제하시겠습니까? 이 작업은 취소할 수 없습니다.", "@deleteConfigConfirmation": {"description": "Delete configuration confirmation message", "placeholders": {"name": {"type": "Object"}}}, "deleteTargetFile": "대상 파일 삭제", "@deleteTargetFile": {"description": "Delete target file dialog title"}, "deleteSourceFRFile": "소스 FR 파일 삭제", "@deleteSourceFRFile": {"description": "Delete source FR file dialog title"}, "deleteFileConfirmation": "파일 \"{name}\"을(를) 삭제하시겠습니까? 이 작업은 취소할 수 없습니다.", "@deleteFileConfirmation": {"description": "Delete file confirmation message", "placeholders": {"name": {"type": "Object"}}}, "noConfigsMessage": "아직 구성이 없습니다. \"+\"를 클릭하여 새 구성을 생성하세요.", "@noConfigsMessage": {"description": "Message shown when there are no configurations"}, "devicePowerOff": "기기가 꺼져 있습니다", "@devicePowerOff": {"description": "Device is powered off"}, "devicePowerOffHint": "기기를 제어하려면 전원을 켜주세요", "@devicePowerOffHint": {"description": "Turn on the power to control the device"}, "powerOn": "전원 켜기", "@powerOn": {"description": "Power on button text"}, "inputOptical1": "광학 1", "@inputOptical1": {"description": "Optical 1 input"}, "inputOptical2": "광학 2", "@inputOptical2": {"description": "Optical 2 input"}, "inputCoaxial1": "동축 1", "@inputCoaxial1": {"description": "Coaxial 1 input"}, "inputCoaxial2": "동축 2", "@inputCoaxial2": {"description": "Coaxial 2 input"}, "inputAes": "AES", "@inputAes": {"description": "AES input"}, "inputIis": "IIS", "@inputIis": {"description": "IIS input"}, "outputDac": "DAC", "@outputDac": {"description": "DAC output"}, "outputPreamp": "프리앤프", "@outputPreamp": {"description": "Preamp output"}, "outputAll": "모두", "@outputAll": {"description": "All outputs"}, "auto": "자동", "@auto": {"description": "Auto mode"}, "enabled": "활성화", "@enabled": {"description": "Enabled state"}, "@disabled": {"description": "Disabled state"}, "standard": "표준", "@standard": {"description": "Standard mode"}, "inverted": "반전", "@inverted": {"description": "Inverted mode"}, "swapped": "교체", "@swapped": {"description": "Swapped mode"}, "displaySignal": "신호", "@displaySignal": {"description": "Signal display"}, "display12V": "12V", "@display12V": {"description": "12V display"}, "displayOff": "끄기", "@displayOff": {"description": "Display off"}, "@multiFunctionKeyPeqSelect": {"description": "PEQ select function key"}, "usbTypeC": "Type-C", "@usbTypeC": {"description": "USB Type-C"}, "usbTypeB": "Type-B", "@usbTypeB": {"description": "USB Type-B"}, "powerTriggerOff": "끄기", "@powerTriggerOff": {"description": "Power trigger off"}, "powerTrigger12V": "12V", "@powerTrigger12V": {"description": "Power trigger 12V"}, "multiFunctionKeyOutputSelect": "출력 선택", "@multiFunctionKeyOutputSelect": {"description": "Output select function key"}, "multiFunctionKeyScreenOff": "화면 끄기", "@multiFunctionKeyScreenOff": {"description": "Screen off function key"}, "usbSelect": "USB 선택", "usbDsdPassthrough": "USB DSD 패스스루", "iisPhase": "IIS 위상", "iisDsdChannel": "IIS DSD 채널"}