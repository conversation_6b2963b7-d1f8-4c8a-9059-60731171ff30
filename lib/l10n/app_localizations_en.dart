// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'Topping Home';

  @override
  String get homeTitle => 'My Products';

  @override
  String get homeAddHint => 'Add devices for better experience';

  @override
  String get addDevice => 'Add Device';

  @override
  String get noDevice => 'No Device';

  @override
  String get addDeviceHint => 'Click to add new device';

  @override
  String get foundDevice => 'Found Device';

  @override
  String get devices => 'Devices';

  @override
  String get loginClick => 'Click to Login';

  @override
  String get about => 'About';

  @override
  String get quickStart => 'Quick Start';

  @override
  String get feedback => 'Feedback';

  @override
  String get customBackground => 'Custom Background';

  @override
  String get logout => 'Log Out';

  @override
  String get registerTitle => 'Register';

  @override
  String get phone => 'Phone Number';

  @override
  String get phoneHint => 'Please enter your phone number';

  @override
  String get verifyCode => 'Verification Code';

  @override
  String get getSmsVerifyCode => 'Get SMS Code';

  @override
  String get agreement => 'I have read and agree to the';

  @override
  String get agreementLink => 'User Agreement';

  @override
  String get agree => 'Agree';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get register => 'Register';

  @override
  String get login => 'Login';

  @override
  String get loginTitle => 'Login';

  @override
  String get loginPhoneHint => 'Please enter your phone number';

  @override
  String get loginVerifyCode => 'Verification Code';

  @override
  String get loginGetSmsVerifyCode => 'Get SMS Code';

  @override
  String get loginPassword => 'Password';

  @override
  String get loginForgetPassword => 'Forgot Password';

  @override
  String get loginFail => 'Login Failed';

  @override
  String get passwordError => 'Incorrect Password';

  @override
  String get phoneNotRegistered => 'Phone Number Not Registered';

  @override
  String get noAccount => 'Don\'t have an account?';

  @override
  String get registerNow => 'Register Now';

  @override
  String get tips => 'Tips';

  @override
  String get graphicCodeHint => 'Please enter the graphic code';

  @override
  String get agreeToTermsHint => 'Please agree to the User Agreement and Privacy Policy';

  @override
  String get verificationCodeHint => 'Please enter the verification code';

  @override
  String get usernameHint => 'Please enter your username';

  @override
  String get passwordHint => 'Please enter your password';

  @override
  String get confirmPasswordHint => 'Please re-enter your password';

  @override
  String get passwordMismatch => 'Passwords do not match';

  @override
  String get registerSuccess => 'Registration Successful';

  @override
  String get registerSuccessHint => 'Please use your phone number and password to log in';

  @override
  String get registerFailed => 'Registration Failed';

  @override
  String get getVerificationCode => 'Get SMS Code';

  @override
  String get verificationCodeSent => 'Verification code has been sent to';

  @override
  String get next => 'Next';

  @override
  String get secondResend => 's to resend';

  @override
  String get settingAccount => 'Account Settings';

  @override
  String get registerComplete => 'Registration Complete';

  @override
  String get username => 'Username';

  @override
  String get password => 'Password';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get inputError => 'Input Error';

  @override
  String get inputCannotBeEmpty => 'Input cannot be empty';

  @override
  String get invalidCaptcha => 'Invalid Captcha';

  @override
  String get forgetPassword => 'Forgot Password';

  @override
  String get forgetPasswordTitle => 'Forgot Password';

  @override
  String get forgetPasswordPhoneTitle => 'Please enter your phone number';

  @override
  String get forgetPasswordPhoneHint => 'Use the bound phone number to get a verification code, then proceed';

  @override
  String get forgetPasswordVerifyCode => 'Verification Code';

  @override
  String get forgetPasswordGetSmsVerifyCode => 'Get SMS Code';

  @override
  String get forgetPasswordNext => 'Next Step';

  @override
  String get forgetPasswordNewPassword => 'New Password';

  @override
  String get forgetPasswordNewPasswordHint => 'Please enter your new password';

  @override
  String get forgetPasswordConfirmPassword => 'Confirm Password';

  @override
  String get forgetPasswordConfirmPasswordHint => 'Please re-enter your new password';

  @override
  String get forgetPasswordReset => 'Reset Password';

  @override
  String get forgetPasswordSuccess => 'Password Reset Successful';

  @override
  String get forgetPasswordSuccessHint => 'Please use the new password to log in';

  @override
  String get forgetPasswordSuccessBack => 'Back to Login';

  @override
  String get forgetPasswordSuccessBackHome => 'Back to Home';

  @override
  String get forgetPasswordSuccessBackLogin => 'Back to Login';

  @override
  String get forgetPasswordSuccessBackRegister => 'Back to Register';

  @override
  String get forgetPasswordSuccessBackForgetPassword => 'Back to Forgot Password';

  @override
  String get aboutTitle => 'About';

  @override
  String get aboutVersion => 'Version';

  @override
  String get aboutVersionHint => '1.0.0';

  @override
  String get aboutUpdate => 'Check for Updates';

  @override
  String get aboutUpdateHint => 'You have the latest version';

  @override
  String get aboutUpdateSuccess => 'Update Successful';

  @override
  String get feedbackTitle => 'Feedback';

  @override
  String get feedbackHint => 'Please leave your valuable comments. We will keep improving our products.';

  @override
  String get feedbackContact => 'Contact Information';

  @override
  String get feedbackContactHint => 'Please enter your contact information';

  @override
  String get feedbackContentHint => 'Please enter your feedback';

  @override
  String get feedbackImage => 'Upload Image';

  @override
  String get feedbackImageHint => 'You can upload up to 5 images';

  @override
  String get feedbackDevice => 'Device Model';

  @override
  String get feedbackDeviceHint => 'Please select your device model';

  @override
  String get feedbackSubmit => 'Submit';

  @override
  String get feedbackSuccess => 'Submission Successful';

  @override
  String get feedbackSuccessHint => 'Thank you for your feedback. We will handle it as soon as possible';

  @override
  String get feedbackType => 'Feedback Type';

  @override
  String get feedbackTypeRequired => 'Please select a feedback type';

  @override
  String get feedbackError => 'Submission Failed, please try again later';

  @override
  String get error => 'Error';

  @override
  String get logoutTitle => 'Log Out';

  @override
  String get logoutHint => 'Are you sure you want to log out?';

  @override
  String get cancel => 'Cancel';

  @override
  String get confirm => 'Confirm';

  @override
  String get deviceNotFoundTitle => 'Device Not Found?';

  @override
  String get deviceNotFoundHint => '1. Please ensure the device is powered on.\n2. Make sure the device is properly connected via Bluetooth.\n3. After completing the above steps, tap the refresh button to search again.\n4. Some phones may need location services enabled for BLE connections. Try enabling location services and refresh again.';

  @override
  String get operateDescription => 'Operation Instructions';

  @override
  String get blueHeadAmp => 'Bluetooth Headphone Amplifier';

  @override
  String get decoderAmp => 'Decoder Amplifier';

  @override
  String get headset => 'Headset';

  @override
  String get bluetoothHeadphoneAmplifier => 'Bluetooth & Headphone';

  @override
  String get player => 'Player';

  @override
  String get inputLinker => 'Manually Enter Linker Control IP';

  @override
  String get connect => 'Connected';

  @override
  String get disconnected => 'Disconnected';

  @override
  String get connecting => 'Connecting';

  @override
  String get disconnecting => 'Disconnecting';

  @override
  String get disconnect => 'Not Connected';

  @override
  String get personalCenter => 'Personal Center';

  @override
  String get editPersonalInfo => 'Edit Personal Information';

  @override
  String get accountSecurity => 'Account Security Settings';

  @override
  String get avatar => 'Avatar';

  @override
  String get gender => 'Gender';

  @override
  String get secret => 'Secret';

  @override
  String get male => 'Male';

  @override
  String get female => 'Female';

  @override
  String get birthday => 'Birthday';

  @override
  String get signature => 'Signature';

  @override
  String get region => 'Region';

  @override
  String get nickname => 'Nickname';

  @override
  String get save => 'Save';

  @override
  String get success => 'Success';

  @override
  String get updateSuccess => 'Update Successful';

  @override
  String get userNotFound => 'User Not Found';

  @override
  String get loginPasswordModify => 'Modify Login Password';

  @override
  String get accountAndBinding => 'Account and Binding Settings';

  @override
  String get modifyPhone => 'Modify Phone Number';

  @override
  String get cancelAccount => 'Cancel Account';

  @override
  String get modifyPassword => 'Modify Password';

  @override
  String get oldPassword => 'Old Password';

  @override
  String get newPassword => 'New Password';

  @override
  String get confirmNewPassword => 'Confirm New Password';

  @override
  String get modifyPasswordSuccess => 'Password Modified Successfully';

  @override
  String get informationIncomplete => 'Incomplete Information';

  @override
  String get oldPasswordError => 'Incorrect Old Password';

  @override
  String get newPasswordAndConfirmPasswordNotConsistent => 'New Password and Confirm Password Do Not Match';

  @override
  String get oldPasswordAndNewPasswordCannotBeTheSame => 'Old Password and New Password Cannot Be the Same';

  @override
  String get newPasswordCannotBeTheSameAsTheOldPassword => 'New Password Cannot Be the Same as the Old Password';

  @override
  String get emailBinding => 'Email Binding';

  @override
  String get notBound => 'Not Bound';

  @override
  String get bound => 'Bound';

  @override
  String get nowEmailVerification => 'Verify Current Email';

  @override
  String get emailVerificationPrompt => 'Please enter the email you want to bind. You can later change the binding or recover your password via email.';

  @override
  String get email => 'Email';

  @override
  String get sendVerificationCode => 'Send Verification Code';

  @override
  String get smsVerificationCode => 'SMS Verification Code';

  @override
  String get verificationCodeHasBeenSent => 'Verification Code Sent';

  @override
  String get accountBindingSuccess => 'Account Successfully Bound';

  @override
  String get emailEmpty => 'Email Cannot Be Empty';

  @override
  String get invalidEmail => 'Invalid Email';

  @override
  String get emailBindSuccess => 'Email Bound Successfully';

  @override
  String get unbindEmail => 'Unbind Email';

  @override
  String get unbindEmailConfirm => 'Are you sure you want to unbind the email?';

  @override
  String get emailUnbindSuccess => 'Email Unbound Successfully';

  @override
  String get boundEmail => 'Bound Email';

  @override
  String get unbind => 'Unbind';

  @override
  String get bind => 'Bind';

  @override
  String get getEmailVerificationCode => 'Get Email Verification Code';

  @override
  String get bindNewPhone => 'Bind New Phone';

  @override
  String get bindNewPhonePrompt => 'Please enter your new phone number and click to receive a verification code for validation.';

  @override
  String get bindNewPhoneSuccess => 'New Phone Bound Successfully';

  @override
  String get bindNewPhoneFailure => 'Failed to Bind New Phone';

  @override
  String get bindNewPhoneFailurePrompt => 'Failed to bind new phone. Please try again later.';

  @override
  String get will => 'Will';

  @override
  String get accountWillBeCancelled => 'The linked account will be cancelled';

  @override
  String get cancellationInstructions => 'Note: Account cancellation is only supported by the account owner and cannot be restored after cancellation. Please proceed cautiously. After formal cancellation, you will no longer be able to use this account, nor retrieve any content or information related to this account, including but not limited to:\n\n1. Personal information under this account (including but not limited to avatar, nickname, bound products, etc.)\n2. All rights under this account (including but not limited to MQA membership, forum points, etc.)\n\n3. All playlists and other privileges under this account\n\n4. Other potential consequences resulting from account cancellation';

  @override
  String get cancellation => 'Cancel';

  @override
  String get confirmCancellation => 'Are you sure you want to cancel the account?';

  @override
  String get cancellationSuccess => 'Cancellation Successful';

  @override
  String get cancellationFailure => 'Cancellation Failed';

  @override
  String get exitApp => 'Exit App';

  @override
  String get exitAppPrompt => 'Are you sure you want to exit the app?';

  @override
  String get phoneUpdateSuccess => 'Phone Number Modified Successfully';

  @override
  String get phoneEmpty => 'Phone Number Cannot Be Empty';

  @override
  String get verificationCodeEmpty => 'Verification Code Cannot Be Empty';

  @override
  String get samePhoneNumber => 'New Phone Number Cannot Be the Same as the Original Phone Number';

  @override
  String get verifyOldPhone => 'Verify Original Phone Number';

  @override
  String get setNewPhone => 'Set New Phone Number';

  @override
  String get oldPhone => 'Original Phone Number';

  @override
  String get newPhone => 'New Phone Number';

  @override
  String get passwordTooShort => 'Password Too Short';

  @override
  String get passwordEmpty => 'Password Cannot Be Empty';

  @override
  String get passwordUpdateSuccess => 'Password Modified Successfully';

  @override
  String get passwordUpdateSuccessRelogin => 'Password Modified Successfully, please log in again';

  @override
  String get scanning => 'Scanning';

  @override
  String get scanningDevicesHint => 'Scanning for nearby Bluetooth devices...';

  @override
  String get startScanningHint => 'Start Scanning';

  @override
  String get manualInputIP => 'Manual Input IP';

  @override
  String get manualInputIPHint => 'Please enter the Linker control IP';

  @override
  String get bluetoothAndDac => 'Bluetooth & DAC';

  @override
  String get streamer => 'Player';

  @override
  String get pleaseInputIP => 'Please enter the IP';

  @override
  String get deviceNotFoundHint1 => '1. Please ensure the device is powered on.';

  @override
  String get deviceNotFoundHint2 => '2. Please keep the device properly connected to your phone\'s Bluetooth';

  @override
  String get deviceNotFoundHint3 => '3. After completing the above steps, click the refresh button to search again.';

  @override
  String get deviceNotFoundHint4 => '4. Some phone systems may require enabling the location service for Bluetooth BLE connections. You can try enabling the location service and refreshing again.';

  @override
  String get invalidQRCode => 'Invalid QR Code';

  @override
  String get scanQRCode => 'Scan QR Code';

  @override
  String get scanQRCodeHint => 'Please scan the QR code';

  @override
  String get scanQRCodeBottomHint => 'Place the QR code in the box to scan automatically';

  @override
  String get noDevicesFound => 'No Devices Found';

  @override
  String get discoveredDevices => 'Discovered Devices';

  @override
  String get discoveredDevicesHint => 'Found Multiple Discovered Devices';

  @override
  String get tapToConnect => 'Tap to Connect';

  @override
  String get availableDevices => 'Available Devices';

  @override
  String get languageCode => 'en';

  @override
  String get deviceNotFound => 'Device Not Found';

  @override
  String get connected => 'Connected';

  @override
  String get battery => 'Battery';

  @override
  String get tryAgain => 'Try Again';

  @override
  String get volumeControl => 'Volume Control';

  @override
  String get unlock => 'Unlock';

  @override
  String get lock => 'Lock';

  @override
  String get audioSettings => 'Audio Settings';

  @override
  String get displayView => 'Display View';

  @override
  String get low => 'Low';

  @override
  String get middle => 'Mid';

  @override
  String get high => 'High';

  @override
  String get equalizer => 'Equalizer';

  @override
  String get eqPreset => 'EQ Preset';

  @override
  String get digitalFilter => 'Digital Filter';

  @override
  String get channelBalance => 'Channel Balance';

  @override
  String get dsdMode => 'DSD Mode';

  @override
  String get systemSettings => 'System Settings';

  @override
  String get displayBrightness => 'Display Brightness';

  @override
  String get displayTimeout => 'Display Timeout';

  @override
  String get autoPowerOff => 'Auto Power Off';

  @override
  String get disabled => 'Disabled';

  @override
  String get ledIndicator => 'LED Indicator';

  @override
  String get connectToAccessSettings => 'Connect to Access Settings';

  @override
  String get connectionInfo => 'Connection Information';

  @override
  String get codec => 'Codec';

  @override
  String get deviceInfo => 'Device Information';

  @override
  String get firmwareVersion => 'Firmware Version';

  @override
  String get serialNumber => 'Serial Number';

  @override
  String get totalPlayTime => 'Total Play Time';

  @override
  String get deleteDevice => 'Delete Device';

  @override
  String get deleteDeviceConfirm => 'Are you sure you want to delete the device?';

  @override
  String get delete => 'Delete';

  @override
  String get preamplifier => 'Preamplifier';

  @override
  String get decodeModeDac => 'DAC';

  @override
  String get displayNormal => 'Normal';

  @override
  String get displayVu => 'VU';

  @override
  String get displayFft => 'FFT';

  @override
  String get inputUsb => 'USB';

  @override
  String get inputOptical => 'Optical';

  @override
  String get inputCoaxial => 'Coaxial';

  @override
  String get inputBluetooth => 'Bluetooth';

  @override
  String get languageZh => 'Chinese';

  @override
  String get languageEn => 'English';

  @override
  String get headphoneGainHigh => 'High Gain';

  @override
  String get headphoneGainLow => 'Low Gain';

  @override
  String get multiFunctionKeyInputSelect => 'Input Select';

  @override
  String get multiFunctionKeyLineOutSelect => 'Line Out Select';

  @override
  String get multiFunctionKeyHeadphoneOutSelect => 'Headphone Out Select';

  @override
  String get multiFunctionKeyHomeSelect => 'Home Select';

  @override
  String get multiFunctionKeyBrightnessSelect => 'Brightness Select';

  @override
  String get multiFunctionKeySleep => 'Sleep';

  @override
  String get multiFunctionKeyPcmFilterSelect => 'PCM Filter Select';

  @override
  String get multiFunctionKeyMute => 'Mute';

  @override
  String get multiFunctionKeyPeqSelect => 'PEQ Select';

  @override
  String get outputClose => 'Off';

  @override
  String get outputSingleEnded => 'RCA';

  @override
  String get outputBalanced => 'XLR';

  @override
  String get outputSingleEndedAndBalanced => 'RCA+XLR';

  @override
  String get powerTriggerSignal => 'Signal';

  @override
  String get powerTriggerVoltage => '12V';

  @override
  String get powerTriggerClose => 'Off';

  @override
  String get screenBrightnessHigh => 'High';

  @override
  String get screenBrightnessMedium => 'Medium';

  @override
  String get screenBrightnessLow => 'Low';

  @override
  String get screenBrightnessAuto => 'Auto';

  @override
  String get themeAurora => 'Aurora';

  @override
  String get themeOrange => 'Orange';

  @override
  String get themePeru => 'Peru';

  @override
  String get themeGreen => 'Bean Green';

  @override
  String get themeKhaki => 'Dark Khaki';

  @override
  String get themeRose => 'Rose Brown';

  @override
  String get themeBlue => 'Blue';

  @override
  String get themePurple => 'Fantasy Purple';

  @override
  String get themeWhite => 'White';

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get lightMode => 'Light Mode';

  @override
  String get darkModeDescription => 'Currently using dark theme';

  @override
  String get lightModeDescription => 'Currently using light theme';

  @override
  String get usbTypeUac2 => 'UAC 2.0';

  @override
  String get usbTypeUac1 => 'UAC 1.0';

  @override
  String get deviceTypeBluetooth => 'Bluetooth';

  @override
  String get deviceTypeDac => 'DAC';

  @override
  String get deviceTypeHeadphone => 'Headphone';

  @override
  String get deviceTypePlayer => 'Player';

  @override
  String get name => 'Name';

  @override
  String get volume => 'Volume';

  @override
  String get headphoneOutput => 'Headphone Output';

  @override
  String get headphoneGain => 'Headphone Gain';

  @override
  String get inputSelect => 'Input Select';

  @override
  String get outputSelect => 'Output Select';

  @override
  String get advanced => 'Advanced';

  @override
  String get advancedSettings => 'Advanced Settings';

  @override
  String get editName => 'Edit Name';

  @override
  String get enterNewName => 'Enter New Name';

  @override
  String get setting => 'Setting';

  @override
  String get peq => 'PEQ';

  @override
  String get guide => 'Guide';

  @override
  String get theme => 'Theme';

  @override
  String get powerTrigger => 'Power Trigger';

  @override
  String get audioBalance => 'Audio Balance';

  @override
  String get filter => 'Filter';

  @override
  String get decodeMode => 'Decode Mode';

  @override
  String get audioMonitoring => 'Audio Bluetooth';

  @override
  String get bluetoothAptx => 'Bluetooth APTX';

  @override
  String get relay => 'Remote Control';

  @override
  String get multiFunctionKey => 'Custom Multi-function Button';

  @override
  String get usbMode => 'USB Mode';

  @override
  String get screenBrightness => 'Screen Brightness';

  @override
  String get language => 'Language';

  @override
  String get resetSettings => 'Reset Advanced Settings';

  @override
  String get restoreFactorySettings => 'Restore Factory Settings';

  @override
  String get channel => 'Channel';

  @override
  String get resetSettingsConfirmation => 'Are you sure you want to reset the advanced settings?';

  @override
  String get restoreFactorySettingsConfirmation => 'Are you sure you want to restore the factory settings?';

  @override
  String get import => 'Import';

  @override
  String get export => 'Export';

  @override
  String get target => 'Target';

  @override
  String get sourceFR => 'Source FR';

  @override
  String get eachFilter => 'Each Filter';

  @override
  String get combinedFilter => 'Combined Filter';

  @override
  String get filteredFR => 'Filtered FR';

  @override
  String get raw => 'Raw';

  @override
  String get compensated => 'Comp';

  @override
  String get preAmplification => 'Preamp';

  @override
  String get gain => 'Gain';

  @override
  String get peakingFilter => 'Peaking Filter';

  @override
  String get lowPassFilter => 'Low-Pass Filter';

  @override
  String get highPassFilter => 'High-Pass Filter';

  @override
  String get lowShelfFilter => 'Low-Shelf Filter';

  @override
  String get highShelfFilter => 'High-Shelf Filter';

  @override
  String get centerFrequency => 'Center Fre';

  @override
  String get connerFrequency => 'Corner Frequency';

  @override
  String get q => 'Q Factor';

  @override
  String get frequency => 'Frequency';

  @override
  String get type => 'Type';

  @override
  String get addFilter => 'Add Filter';

  @override
  String get mode => 'Mode';

  @override
  String get import_target => 'Import Target';

  @override
  String get import_sourceFR => 'Import Source FR';

  @override
  String get selectDataFile => 'Select Data File';

  @override
  String get feedbackContent => 'Feedback Content';

  @override
  String get problemType => 'Problem Type';

  @override
  String get selectDeviceType => 'Select Device Type';

  @override
  String get device => 'Device';

  @override
  String get addPicture => 'Add Picture (Optional)';

  @override
  String get contact => 'Contact';

  @override
  String get feedbackTypeFeatureSuggestion => 'Feature Suggestion';

  @override
  String get feedbackTypeBugReport => 'Bug Report';

  @override
  String get feedbackTypeUIImprovement => 'UI Improvement';

  @override
  String get feedbackTypeOther => 'Other';

  @override
  String get checkUpdate => 'Check for Updates';

  @override
  String get checking => 'Checking...,';

  @override
  String get alreadyLatestVersion => 'Already Latest Version';

  @override
  String get failed => 'Check update failed';

  @override
  String get foundNewVersion => 'New Version Available';

  @override
  String get currentVersion => 'Current Version';

  @override
  String get newVersion => 'New Version';

  @override
  String get updateContent => 'Update Content:';

  @override
  String get later => 'Later';

  @override
  String get updateNow => 'Update Now';

  @override
  String get downloading => 'Downloading...';

  @override
  String get downloadCompleted => 'Download completed';

  @override
  String get downloadFailed => 'Download failed';

  @override
  String get storagePermissionDenied => 'Storage permission denied';

  @override
  String get cannotGetDownloadPath => 'Cannot get download path';

  @override
  String get checkUpdateFailed => 'Check update failed';

  @override
  String get installPermissionDenied => 'Install permission denied';

  @override
  String get failedToLoadContent => 'Failed to load content';

  @override
  String get lastUpdated => 'Last Updated';

  @override
  String get termsAndConditions => 'Terms and Conditions';

  @override
  String get agreementDialogContent => 'By clicking Agree, you agree to the User Agreement and Privacy Policy';

  @override
  String get userAgreement => 'User Agreement';

  @override
  String get privacyPolicyText => 'Privacy Policy';

  @override
  String get disagree => 'Disagree';

  @override
  String get exportSuccess => 'Export Successful';

  @override
  String get fileSavedTo => 'File saved to:';

  @override
  String get fileContentPreview => 'File content preview:';

  @override
  String get ok => 'OK';

  @override
  String get openFileLocation => 'Open File Location';

  @override
  String get sharedFilterFile => 'Shared Filter File';

  @override
  String get sharedFile => 'Shared File';

  @override
  String get cannotOpenFileOrItsDirectory => 'Cannot open file or its directory';

  @override
  String get errorOpeningFileLocation => 'Error opening file location:';

  @override
  String get errorSharingFile => 'Error sharing file';

  @override
  String get cannotParseSelectedFile => 'Cannot parse selected file. Please ensure it is a valid CSV format and contains frequency and response data.';

  @override
  String get cannotExtractValidFrequencyAndResponseDataFromCSV => 'Cannot extract valid frequency and response data from CSV.';

  @override
  String get importSuccess => 'Import Successful';

  @override
  String get successfullyImportedFile => 'Successfully imported file';

  @override
  String get errorImportingFile => 'Error importing file';

  @override
  String get noDataToExport => 'No data to export.';

  @override
  String get fileSavedToAppFolder => 'File saved to app folder:';

  @override
  String get errorSavingFile => 'Error saving file:';

  @override
  String get saveMergedFilterFile => 'Save Merged Filter File';

  @override
  String get startUsing => 'Start Using';

  @override
  String get nextPage => 'Next Page';

  @override
  String get background => 'Background';

  @override
  String get confirmDelete => 'Confirm Delete';

  @override
  String get confirmDeleteHint => 'Are you sure you want to delete this background image?';

  @override
  String get opacity => 'Opacity';

  @override
  String get blur => 'Blur';

  @override
  String get selectFromAlbum => 'Select from Album';

  @override
  String get takePhoto => 'Take Photo';

  @override
  String get addBackground => 'Add Background';

  @override
  String get deleteBackgroundFailed => 'Failed to delete background';

  @override
  String get saveBackgroundFailed => 'Failed to save background';

  @override
  String get errorExportingFile => 'Error exporting file';

  @override
  String get skip => 'Skip';

  @override
  String get version => 'Version';

  @override
  String get copyright => ' 2025 Topping. All rights reserved.';

  @override
  String get newFirmwareAvailable => 'New Firmware Available';

  @override
  String get tip => 'Tip';

  @override
  String get firmwareUpgrade => 'Firmware Upgrade';

  @override
  String get firmwareUpgrading => 'Upgrading Firmware';

  @override
  String get firmwareUpgradeSuccess => 'Firmware Upgrade Success';

  @override
  String get firmwareUpgradeFailed => 'Firmware Upgrade Failed';

  @override
  String get firmwareUpgradeConfirm => 'Are you sure to upgrade firmware?';

  @override
  String get firmwareUpgradeWarning => 'Please do not disconnect the device during upgrade';

  @override
  String get firmwareSize => 'Firmware Size';

  @override
  String get firmwareDescription => 'Description';

  @override
  String get firmwareForceUpdate => 'Force Update';

  @override
  String get firmwareDownloading => 'Downloading Firmware';

  @override
  String get firmwareInstalling => 'Installing Firmware';

  @override
  String get settingResetFail => 'Reset Failed';

  @override
  String get firmwareUpdateTitle => 'Firmware Update';

  @override
  String get firmwareUpdateNewFirmwareFound => 'New Firmware Found';

  @override
  String get firmwareUpdateFirmwareName => 'Firmware Name';

  @override
  String get firmwareUpdateVersion => 'Version';

  @override
  String get firmwareUpdateDeviceModel => 'Device Model';

  @override
  String get firmwareUpdateFileSize => 'File Size';

  @override
  String get firmwareUpdateDescription => 'Update Notes';

  @override
  String get firmwareUpdateMandatoryUpdateNote => '* This version is a mandatory update';

  @override
  String get firmwareUpdateStatus => 'Update Status';

  @override
  String get firmwareUpdateDownloading => 'Downloading';

  @override
  String get firmwareUpdateUpgrading => 'Upgrading';

  @override
  String get firmwareUpdateDoNotDisconnect => 'Please do not disconnect or turn off the device';

  @override
  String get firmwareUpdateReadyToUpdate => 'Ready to Update';

  @override
  String get firmwareUpdateCancel => 'Cancel Update';

  @override
  String get firmwareUpdateStart => 'Start Update';

  @override
  String get firmwareUpdateLatestVersion => 'Already Latest Version';

  @override
  String get firmwareUpdateNoNeed => 'No firmware update needed';

  @override
  String get firmwareUpdateBack => 'Back';

  @override
  String get firmwareUpdateSuccessTitle => 'Update Successful';

  @override
  String get firmwareUpdateSuccessMessage => 'Device firmware updated to the latest version';

  @override
  String get firmwareUpdateSuccessRebootMessage => 'Upgrade successful, device is rebooting...';

  @override
  String get firmwareUpdateDownloadingTitle => 'Downloading';

  @override
  String get firmwareUpdateDownloadingMessage => 'Downloading firmware file...';

  @override
  String get firmwareUpdateErrorTitle => 'Error';

  @override
  String get firmwareUpdateDownloadFailed => 'Firmware download failed';

  @override
  String get firmwareUpdateUpgradingTitle => 'Upgrading';

  @override
  String get firmwareUpdateUpgradingMessage => 'Starting firmware upgrade...';

  @override
  String get firmwareUpdateSetDeviceFailed => 'Set device failed';

  @override
  String firmwareUpdateErrorDuringUpdate(String error) {
    return 'Error during upgrade: $error';
  }

  @override
  String get firmwareUpdateCancelledTitle => 'Cancelled';

  @override
  String get firmwareUpdateCancelledMessage => 'Firmware upgrade cancelled';

  @override
  String get commonConfirm => 'Confirm';

  @override
  String get checkFirmwareUpdate => 'Check Firmware Update';

  @override
  String get deviceRebootTitle => 'Device Reboot';

  @override
  String get deviceRebootMessage => 'The device is restarting, please wait...';

  @override
  String get infoTitle => 'Information';

  @override
  String errorOpeningFile(String error) {
    return 'Error opening file: $error';
  }

  @override
  String get fileExported => 'File exported';

  @override
  String get shareFile => 'Share File';

  @override
  String eqWavePainterStarted(String size) {
    return 'EQ Wave Painter started. Size: $size';
  }

  @override
  String targetRawDataPrepared(int count) {
    return 'Target raw data prepared (interpolated): $count points';
  }

  @override
  String sourceFRRawDataPrepared(int count) {
    return 'Source FR raw data prepared (interpolated): $count points';
  }

  @override
  String combinedRawDataPrepared(int count) {
    return 'Combined raw data prepared: $count points';
  }

  @override
  String individualBandsRawDataPrepared(int bandCount, int pointCount) {
    return 'Individual bands raw data prepared: $bandCount bands, total points: $pointCount';
  }

  @override
  String filteredFRRawDataPrepared(int count) {
    return 'Filtered FR raw data prepared (based on interpolated source): $count points';
  }

  @override
  String dynamicDbRangeCalculated(String min, String max) {
    return 'Dynamic dB range calculated: Min=$min, Max=$max';
  }

  @override
  String get coordinateConverterCreated => 'Coordinate converter created and chart height adjusted';

  @override
  String get gridAndLabelsDrawn => 'Grid and labels drawn';

  @override
  String get drawingTargetCurve => 'Drawing Target Curve...';

  @override
  String get drawingSourceFRCurve => 'Drawing Source FR Curve...';

  @override
  String get drawingIndividualBands => 'Drawing Individual Bands...';

  @override
  String get drawingCombinedCurve => 'Drawing Combined Curve...';

  @override
  String get drawingFilteredFRCurve => 'Drawing Filtered FR Curve...';

  @override
  String get drawingFlatLine => 'Drawing Flat Line...';

  @override
  String get eqWavePainterFinished => 'EQ Wave Painter finished';

  @override
  String finalDynamicDbRange(String min, String max) {
    return 'Final Dynamic dB range: Min=$min, Max=$max';
  }

  @override
  String get peqSettings => 'PEQ Settings';

  @override
  String get importExport => 'Import & Export';

  @override
  String get configManagement => 'Configuration Management';

  @override
  String get addConfig => 'Add Configuration';

  @override
  String get configName => 'Configuration Name';

  @override
  String get description => 'Description (Optional)';

  @override
  String get configNameHint => 'e.g. Bass Boost, Clear Vocals, etc.';

  @override
  String get configDescriptionHint => 'Briefly describe the purpose of this configuration';

  @override
  String get add => 'Add';

  @override
  String get addNewConfig => 'Add New Configuration';

  @override
  String get importTarget => 'Import Target';

  @override
  String get importSourceFR => 'Import Source FR';

  @override
  String get exportCombinedFilter => 'Export Combined Filter';

  @override
  String get targetFR => 'Target FR';

  @override
  String get editConfig => 'Edit Configuration';

  @override
  String get deleteConfig => 'Delete Configuration';

  @override
  String deleteConfigConfirmation(Object name) {
    return 'Are you sure you want to delete the configuration \"$name\"? This action cannot be undone.';
  }

  @override
  String get deleteTargetFile => 'Delete Target File';

  @override
  String get deleteSourceFRFile => 'Delete Source FR File';

  @override
  String deleteFileConfirmation(Object name) {
    return 'Are you sure you want to delete the file \"$name\"? This action cannot be undone.';
  }

  @override
  String get noConfigsMessage => 'No configurations yet. Click \"+\" to create a new one.';

  @override
  String get devicePowerOff => 'Device is powered off';

  @override
  String get devicePowerOffHint => 'Turn on the power to control the device';

  @override
  String get powerOn => 'Power On';

  @override
  String get inputOptical1 => 'Optical 1';

  @override
  String get inputOptical2 => 'Optical 2';

  @override
  String get inputCoaxial1 => 'Coaxial 1';

  @override
  String get inputCoaxial2 => 'Coaxial 2';

  @override
  String get inputAes => 'AES';

  @override
  String get inputIis => 'IIS';

  @override
  String get outputDac => 'DAC';

  @override
  String get outputPreamp => 'Preamp';

  @override
  String get outputAll => 'All';

  @override
  String get auto => 'Auto';

  @override
  String get enabled => 'Enabled';

  @override
  String get standard => 'Standard';

  @override
  String get inverted => 'Inverted';

  @override
  String get swapped => 'Swapped';

  @override
  String get displaySignal => 'Signal';

  @override
  String get display12V => '12V';

  @override
  String get displayOff => 'Off';

  @override
  String get usbTypeC => 'Type-C';

  @override
  String get usbTypeB => 'Type-B';

  @override
  String get powerTriggerOff => 'Off';

  @override
  String get powerTrigger12V => '12V';

  @override
  String get multiFunctionKeyOutputSelect => 'Output Select';

  @override
  String get multiFunctionKeyScreenOff => 'Screen Off';

  @override
  String get usbSelect => 'USB Select';

  @override
  String get usbDsdPassthrough => 'USB DSD Passthrough';

  @override
  String get iisPhase => 'IIS Phase';

  @override
  String get iisDsdChannel => 'IIS DSD Channel';
}
