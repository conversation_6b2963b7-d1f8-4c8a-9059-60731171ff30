// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appName => '拓品之家';

  @override
  String get homeTitle => '我的产品';

  @override
  String get homeAddHint => '添加产品，带来更多更好的操作体验';

  @override
  String get addDevice => '添加设备';

  @override
  String get noDevice => '暂无设备';

  @override
  String get addDeviceHint => '点击添加新设备';

  @override
  String get foundDevice => '发现设备';

  @override
  String get devices => '设备';

  @override
  String get loginClick => '点击登录';

  @override
  String get about => '关于';

  @override
  String get quickStart => '快速入门';

  @override
  String get feedback => '意见反馈';

  @override
  String get customBackground => '自定义背景';

  @override
  String get logout => '退出登录';

  @override
  String get registerTitle => '注册';

  @override
  String get phone => '手机号';

  @override
  String get phoneHint => '请输入手机号';

  @override
  String get verifyCode => '验证码';

  @override
  String get getSmsVerifyCode => '获取短信验证码';

  @override
  String get agreement => '我已阅读并同意';

  @override
  String get agreementLink => '《用户协议》';

  @override
  String get agree => '同意';

  @override
  String get privacyPolicy => '《隐私政策》';

  @override
  String get register => '注册';

  @override
  String get login => '登录';

  @override
  String get loginTitle => '登录';

  @override
  String get loginPhoneHint => '请输入手机号';

  @override
  String get loginVerifyCode => '验证码';

  @override
  String get loginGetSmsVerifyCode => '获取短信验证码';

  @override
  String get loginPassword => '密码登录';

  @override
  String get loginForgetPassword => '忘记密码';

  @override
  String get loginFail => '登录失败';

  @override
  String get passwordError => '密码错误';

  @override
  String get phoneNotRegistered => '手机号未注册';

  @override
  String get noAccount => '还没有账号？';

  @override
  String get registerNow => '立即注册';

  @override
  String get tips => '提示';

  @override
  String get graphicCodeHint => '请输入图形验证码';

  @override
  String get agreeToTermsHint => '请同意用户协议和隐私政策';

  @override
  String get verificationCodeHint => '请输入验证码';

  @override
  String get usernameHint => '请输入用户名';

  @override
  String get passwordHint => '请输入密码';

  @override
  String get confirmPasswordHint => '请再次输入密码';

  @override
  String get passwordMismatch => '两次输入密码不一致';

  @override
  String get registerSuccess => '注册成功';

  @override
  String get registerSuccessHint => '请使用手机号和密码登录';

  @override
  String get registerFailed => '注册失败';

  @override
  String get getVerificationCode => '获取短信验证码';

  @override
  String get verificationCodeSent => '验证码已发送至';

  @override
  String get next => '下一步';

  @override
  String get secondResend => 's后重新发送';

  @override
  String get settingAccount => '账号设置';

  @override
  String get registerComplete => '完成注册';

  @override
  String get username => '用户名';

  @override
  String get password => '密码';

  @override
  String get confirmPassword => '确认密码';

  @override
  String get inputError => '输入有误';

  @override
  String get inputCannotBeEmpty => '输入不能为空';

  @override
  String get invalidCaptcha => '验证码错误';

  @override
  String get forgetPassword => '忘记密码';

  @override
  String get forgetPasswordTitle => '忘记密码';

  @override
  String get forgetPasswordPhoneTitle => '请输入手机号';

  @override
  String get forgetPasswordPhoneHint => '先通过您已绑定的手机号获取验证码，再进行下一步操作';

  @override
  String get forgetPasswordVerifyCode => '验证码';

  @override
  String get forgetPasswordGetSmsVerifyCode => '获取短信验证码';

  @override
  String get forgetPasswordNext => '下一步';

  @override
  String get forgetPasswordNewPassword => '新密码';

  @override
  String get forgetPasswordNewPasswordHint => '请输入新密码';

  @override
  String get forgetPasswordConfirmPassword => '确认密码';

  @override
  String get forgetPasswordConfirmPasswordHint => '请再次输入新密码';

  @override
  String get forgetPasswordReset => '重置密码';

  @override
  String get forgetPasswordSuccess => '重置密码成功';

  @override
  String get forgetPasswordSuccessHint => '请使用新密码登录';

  @override
  String get forgetPasswordSuccessBack => '返回登录';

  @override
  String get forgetPasswordSuccessBackHome => '返回首页';

  @override
  String get forgetPasswordSuccessBackLogin => '返回登录';

  @override
  String get forgetPasswordSuccessBackRegister => '返回注册';

  @override
  String get forgetPasswordSuccessBackForgetPassword => '返回忘记密码';

  @override
  String get aboutTitle => '关于';

  @override
  String get aboutVersion => '版本号';

  @override
  String get aboutVersionHint => '1.0.0';

  @override
  String get aboutUpdate => '检查更新';

  @override
  String get aboutUpdateHint => '当前已是最新版本';

  @override
  String get aboutUpdateSuccess => '更新成功';

  @override
  String get feedbackTitle => '意见反馈';

  @override
  String get feedbackHint => '请留下您的宝贵意见，我们将不断优化产品';

  @override
  String get feedbackContact => '联系方式';

  @override
  String get feedbackContactHint => '请输入您的联系方式';

  @override
  String get feedbackContentHint => '请输入您的反馈内容';

  @override
  String get feedbackImage => '上传图片';

  @override
  String get feedbackImageHint => '最多上传5张图片';

  @override
  String get feedbackDevice => '设备型号';

  @override
  String get feedbackDeviceHint => '请选择您的设备型号';

  @override
  String get feedbackSubmit => '提交';

  @override
  String get feedbackSuccess => '提交成功';

  @override
  String get feedbackSuccessHint => '感谢您的反馈，我们将尽快处理';

  @override
  String get feedbackType => '反馈类型';

  @override
  String get feedbackTypeRequired => '请选择反馈类型';

  @override
  String get feedbackError => '提交失败，请稍后重试';

  @override
  String get error => '错误';

  @override
  String get logoutTitle => '退出登录';

  @override
  String get logoutHint => '确定要退出登录吗？';

  @override
  String get cancel => '取消';

  @override
  String get confirm => '确定';

  @override
  String get deviceNotFoundTitle => '未找到您的设备？';

  @override
  String get deviceNotFoundHint => '1、请确保设备处于开机状态\n2、请保持设备与手机蓝牙正常连接\n3、完成上述步骤，点击刷新按钮重新搜索。\n4、部分手机系统可能需要开启定位功能后才能进行蓝牙BLE连接，您可以尝试开启定位功能后再次刷新';

  @override
  String get operateDescription => '操作说明';

  @override
  String get blueHeadAmp => '蓝牙耳放';

  @override
  String get decoderAmp => '解码耳放';

  @override
  String get headset => '耳机';

  @override
  String get bluetoothHeadphoneAmplifier => '蓝牙及耳机';

  @override
  String get player => '播放器';

  @override
  String get inputLinker => '手动输入Linker控制IP';

  @override
  String get connect => '已连接';

  @override
  String get disconnected => '已断开';

  @override
  String get connecting => '正在连接';

  @override
  String get disconnecting => '正在断开';

  @override
  String get disconnect => '未连接';

  @override
  String get personalCenter => '个人中心';

  @override
  String get editPersonalInfo => '编辑个人信息';

  @override
  String get accountSecurity => '账号安全设置';

  @override
  String get avatar => '头像';

  @override
  String get gender => '性别';

  @override
  String get secret => '保密';

  @override
  String get male => '男';

  @override
  String get female => '女';

  @override
  String get birthday => '生日';

  @override
  String get signature => '个性签名';

  @override
  String get region => '地区';

  @override
  String get nickname => '昵称';

  @override
  String get save => '保存';

  @override
  String get success => '成功';

  @override
  String get updateSuccess => '更新成功';

  @override
  String get userNotFound => '用户不存在';

  @override
  String get loginPasswordModify => '登录密码修改';

  @override
  String get accountAndBinding => '账号与绑定设置';

  @override
  String get modifyPhone => '修改手机号';

  @override
  String get cancelAccount => '注销账号';

  @override
  String get modifyPassword => '修改密码';

  @override
  String get oldPassword => '旧密码';

  @override
  String get newPassword => '新密码';

  @override
  String get confirmNewPassword => '确认新密码';

  @override
  String get modifyPasswordSuccess => '修改密码成功';

  @override
  String get informationIncomplete => '信息填写不完整';

  @override
  String get oldPasswordError => '旧密码错误';

  @override
  String get newPasswordAndConfirmPasswordNotConsistent => '新密码与确认密码不一致';

  @override
  String get oldPasswordAndNewPasswordCannotBeTheSame => '旧密码与新密码不能相同';

  @override
  String get newPasswordCannotBeTheSameAsTheOldPassword => '新密码不能与旧密码相同';

  @override
  String get emailBinding => '邮箱绑定';

  @override
  String get notBound => '未绑定';

  @override
  String get bound => '已绑定';

  @override
  String get nowEmailVerification => '现在邮箱验证';

  @override
  String get emailVerificationPrompt => '请输入你需要绑定的邮箱，后续可以进行邮箱改绑和邮箱密码找回';

  @override
  String get email => '邮箱';

  @override
  String get sendVerificationCode => '发送验证码';

  @override
  String get smsVerificationCode => '短信验证码';

  @override
  String get verificationCodeHasBeenSent => '验证码已发送';

  @override
  String get accountBindingSuccess => '账号绑定成功';

  @override
  String get emailEmpty => '邮箱不能为空';

  @override
  String get invalidEmail => '邮箱格式不正确';

  @override
  String get emailBindSuccess => '邮箱绑定成功';

  @override
  String get unbindEmail => '解绑邮箱';

  @override
  String get unbindEmailConfirm => '确定要解绑邮箱吗？';

  @override
  String get emailUnbindSuccess => '邮箱解绑成功';

  @override
  String get boundEmail => '已绑定邮箱';

  @override
  String get unbind => '解绑';

  @override
  String get bind => '绑定';

  @override
  String get getEmailVerificationCode => '获取邮箱验证码';

  @override
  String get bindNewPhone => '绑定新手机';

  @override
  String get bindNewPhonePrompt => '请输入您的新手机号，点击获取验证码完成验证';

  @override
  String get bindNewPhoneSuccess => '绑定新手机成功';

  @override
  String get bindNewPhoneFailure => '绑定新手机失败';

  @override
  String get bindNewPhoneFailurePrompt => '绑定新手机失败，请稍后重试';

  @override
  String get will => '将';

  @override
  String get accountWillBeCancelled => '所绑定的账号注销';

  @override
  String get cancellationInstructions => '注意：注销账号仅支持账号本人操作，且注销后不支持恢复使用，请您审中操作。\n正式注销后，您将无法再使用本账号，也无法找回账号中以及账号相关的任何内容信息，包括但不限于：\n\n1、该账号下的个人信息（包括但不限于头像、昵称、绑定产品等）\n2、该账号下的各类权益（包括但不限于MQA会员、论坛积分等）\n\n3、该账号下的各类权益（包括但不限于播放列表等）\n\n4、其他应账号注销所（可能）产生的结果';

  @override
  String get cancellation => '注销';

  @override
  String get confirmCancellation => '确定要注销账号吗？';

  @override
  String get cancellationSuccess => '注销成功';

  @override
  String get cancellationFailure => '注销失败';

  @override
  String get exitApp => '退出应用';

  @override
  String get exitAppPrompt => '确定要退出应用吗？';

  @override
  String get phoneUpdateSuccess => '手机号修改成功';

  @override
  String get phoneEmpty => '手机号不能为空';

  @override
  String get verificationCodeEmpty => '验证码不能为空';

  @override
  String get samePhoneNumber => '新手机号与原手机号相同';

  @override
  String get verifyOldPhone => '验证原手机号';

  @override
  String get setNewPhone => '设置新手机号';

  @override
  String get oldPhone => '原手机号';

  @override
  String get newPhone => '新手机号';

  @override
  String get passwordTooShort => '密码长度不能少于6位';

  @override
  String get passwordEmpty => '密码不能为空';

  @override
  String get passwordUpdateSuccess => '密码修改成功';

  @override
  String get passwordUpdateSuccessRelogin => '密码修改成功，请重新登录';

  @override
  String get scanning => '正在扫描';

  @override
  String get scanningDevicesHint => '正在扫描附近的蓝牙设备...';

  @override
  String get startScanningHint => '开始扫描';

  @override
  String get manualInputIP => '手动输入IP';

  @override
  String get manualInputIPHint => '请输入Linker控制IP';

  @override
  String get bluetoothAndDac => '蓝牙及DAC';

  @override
  String get streamer => '播放器';

  @override
  String get pleaseInputIP => '请输入IP';

  @override
  String get deviceNotFoundHint1 => '1、请确保设备处于开机状态。';

  @override
  String get deviceNotFoundHint2 => '2、请保持设备与手机蓝牙正常连接。';

  @override
  String get deviceNotFoundHint3 => '3、完成上述步骤，点击刷新按钮重新搜索。';

  @override
  String get deviceNotFoundHint4 => '4、部分手机系统可能需要开启定位功能后才能进行蓝牙BLE连接，您可以尝试开启定位功能后再次刷新。';

  @override
  String get invalidQRCode => '无效二维码';

  @override
  String get scanQRCode => '扫描二维码';

  @override
  String get scanQRCodeHint => '请扫描设备上的二维码';

  @override
  String get scanQRCodeBottomHint => '请将二维码放入框内，即可自动扫描';

  @override
  String get noDevicesFound => '未找到设备';

  @override
  String get discoveredDevices => '可连接产品';

  @override
  String get discoveredDevicesHint => '发现多个可连接产品';

  @override
  String get tapToConnect => '轻触连接';

  @override
  String get availableDevices => '可用设备';

  @override
  String get languageCode => 'zh';

  @override
  String get deviceNotFound => '设备未找到';

  @override
  String get connected => '已连接';

  @override
  String get battery => '电量';

  @override
  String get tryAgain => '再试一次';

  @override
  String get volumeControl => '音量控制';

  @override
  String get unlock => '解锁';

  @override
  String get lock => '锁定';

  @override
  String get audioSettings => '音频设置';

  @override
  String get displayView => '显示界面';

  @override
  String get low => '低';

  @override
  String get middle => '中';

  @override
  String get high => '高';

  @override
  String get equalizer => '均衡器';

  @override
  String get eqPreset => 'EQ预设';

  @override
  String get digitalFilter => '数字滤波器';

  @override
  String get channelBalance => '声道平衡';

  @override
  String get dsdMode => 'DSD模式';

  @override
  String get systemSettings => '系统设置';

  @override
  String get displayBrightness => '显示亮度';

  @override
  String get displayTimeout => '显示超时';

  @override
  String get autoPowerOff => '自动关机';

  @override
  String get disabled => '禁用';

  @override
  String get ledIndicator => 'LED指示灯';

  @override
  String get connectToAccessSettings => '连接到访问设置';

  @override
  String get connectionInfo => '连接信息';

  @override
  String get codec => '编解码器';

  @override
  String get deviceInfo => '设备信息';

  @override
  String get firmwareVersion => '固件版本';

  @override
  String get serialNumber => '序列号';

  @override
  String get totalPlayTime => '总播放时间';

  @override
  String get deleteDevice => '删除设备';

  @override
  String get deleteDeviceConfirm => '确定要删除设备吗？';

  @override
  String get delete => '删除';

  @override
  String get preamplifier => '前级';

  @override
  String get decodeModeDac => 'DAC';

  @override
  String get displayNormal => '正常';

  @override
  String get displayVu => 'VU表';

  @override
  String get displayFft => '频谱';

  @override
  String get inputUsb => 'USB';

  @override
  String get inputOptical => '光纤';

  @override
  String get inputCoaxial => '同轴';

  @override
  String get inputBluetooth => '蓝牙';

  @override
  String get languageZh => '中文';

  @override
  String get languageEn => '英文';

  @override
  String get headphoneGainHigh => '高增益';

  @override
  String get headphoneGainLow => '低增益';

  @override
  String get multiFunctionKeyInputSelect => '输入选择';

  @override
  String get multiFunctionKeyLineOutSelect => '线路输出选择';

  @override
  String get multiFunctionKeyHeadphoneOutSelect => '耳放输出选择';

  @override
  String get multiFunctionKeyHomeSelect => '主页选择';

  @override
  String get multiFunctionKeyBrightnessSelect => '亮度选择';

  @override
  String get multiFunctionKeySleep => '息屏';

  @override
  String get multiFunctionKeyPcmFilterSelect => 'PCM滤波器选择';

  @override
  String get multiFunctionKeyMute => '静音';

  @override
  String get multiFunctionKeyPeqSelect => 'PEQ选择';

  @override
  String get outputClose => '关闭';

  @override
  String get outputSingleEnded => 'RCA';

  @override
  String get outputBalanced => 'XLR';

  @override
  String get outputSingleEndedAndBalanced => 'RCA+XLR';

  @override
  String get powerTriggerSignal => '信号';

  @override
  String get powerTriggerVoltage => '12V';

  @override
  String get powerTriggerClose => '关闭';

  @override
  String get screenBrightnessHigh => '高';

  @override
  String get screenBrightnessMedium => '中';

  @override
  String get screenBrightnessLow => '低';

  @override
  String get screenBrightnessAuto => '自动';

  @override
  String get themeAurora => '极光';

  @override
  String get themeOrange => '橙色';

  @override
  String get themePeru => '秘鲁色';

  @override
  String get themeGreen => '豆绿色';

  @override
  String get themeKhaki => '深卡其色';

  @override
  String get themeRose => '玫瑰棕色';

  @override
  String get themeBlue => '蓝色';

  @override
  String get themePurple => '幻紫色';

  @override
  String get themeWhite => '白色';

  @override
  String get darkMode => '深色模式';

  @override
  String get lightMode => '浅色模式';

  @override
  String get darkModeDescription => '当前使用深色主题';

  @override
  String get lightModeDescription => '当前使用浅色主题';

  @override
  String get usbTypeUac2 => 'UAC 2.0';

  @override
  String get usbTypeUac1 => 'UAC 1.0';

  @override
  String get deviceTypeBluetooth => '蓝牙';

  @override
  String get deviceTypeDac => 'DAC';

  @override
  String get deviceTypeHeadphone => '耳放';

  @override
  String get deviceTypePlayer => '播放器';

  @override
  String get name => '名称';

  @override
  String get volume => '音量';

  @override
  String get headphoneOutput => '耳放输出';

  @override
  String get headphoneGain => '耳放增益';

  @override
  String get inputSelect => '输入选择';

  @override
  String get outputSelect => '输出选择';

  @override
  String get advanced => '高级';

  @override
  String get advancedSettings => '高级设置';

  @override
  String get editName => '修改名称';

  @override
  String get enterNewName => '请输入新名称';

  @override
  String get setting => '设置';

  @override
  String get peq => '均衡器';

  @override
  String get guide => '操作指南';

  @override
  String get theme => '主题';

  @override
  String get powerTrigger => '开关机触发';

  @override
  String get audioBalance => '声道平衡';

  @override
  String get filter => '滤波器';

  @override
  String get decodeMode => '解码模式';

  @override
  String get audioMonitoring => '音频蓝牙';

  @override
  String get bluetoothAptx => '蓝牙APTX';

  @override
  String get relay => '遥控器';

  @override
  String get multiFunctionKey => '自定义多功能按键';

  @override
  String get usbMode => 'USB模式';

  @override
  String get screenBrightness => '屏幕亮度';

  @override
  String get language => '语言';

  @override
  String get resetSettings => '恢复高级设置';

  @override
  String get restoreFactorySettings => '恢复出厂设置';

  @override
  String get channel => '声道';

  @override
  String get resetSettingsConfirmation => '确定要恢复高级设置吗？';

  @override
  String get restoreFactorySettingsConfirmation => '确定要恢复出厂设置吗？';

  @override
  String get import => '导入';

  @override
  String get export => '导出';

  @override
  String get target => '目标';

  @override
  String get sourceFR => '源频响';

  @override
  String get eachFilter => '每个滤波';

  @override
  String get combinedFilter => '合并滤波';

  @override
  String get filteredFR => '滤波后频响';

  @override
  String get raw => '原始';

  @override
  String get compensated => '补偿';

  @override
  String get preAmplification => '前级';

  @override
  String get gain => '增益';

  @override
  String get peakingFilter => '峰值滤波';

  @override
  String get lowPassFilter => '低通滤波';

  @override
  String get highPassFilter => '高通滤波';

  @override
  String get lowShelfFilter => '低滤搁架';

  @override
  String get highShelfFilter => '高滤搁架';

  @override
  String get centerFrequency => '中心频率';

  @override
  String get connerFrequency => '拐点频率';

  @override
  String get q => 'Q值';

  @override
  String get frequency => '频率';

  @override
  String get type => '类型';

  @override
  String get addFilter => '添加滤波';

  @override
  String get mode => '模式';

  @override
  String get import_target => '导入目标曲线';

  @override
  String get import_sourceFR => '导入源频响曲线';

  @override
  String get selectDataFile => '选择数据文件';

  @override
  String get feedbackContent => '反馈内容';

  @override
  String get problemType => '问题类型';

  @override
  String get selectDeviceType => '请选择设备类型';

  @override
  String get device => '设备';

  @override
  String get addPicture => '添加图片';

  @override
  String get contact => '联系方式';

  @override
  String get feedbackTypeFeatureSuggestion => '功能建议';

  @override
  String get feedbackTypeBugReport => 'Bug反馈';

  @override
  String get feedbackTypeUIImprovement => 'UI优化';

  @override
  String get feedbackTypeOther => '其他';

  @override
  String get checkUpdate => '检查更新';

  @override
  String get checking => '检查中...';

  @override
  String get alreadyLatestVersion => '已是最新版本';

  @override
  String get failed => 'Check update failed';

  @override
  String get foundNewVersion => '发现新版本';

  @override
  String get currentVersion => '当前版本';

  @override
  String get newVersion => '新版本';

  @override
  String get updateContent => '更新内容';

  @override
  String get later => '以后再说';

  @override
  String get updateNow => '立即更新';

  @override
  String get downloading => '下载中...';

  @override
  String get downloadCompleted => '下载完成';

  @override
  String get downloadFailed => '下载失败';

  @override
  String get storagePermissionDenied => '存储权限被拒绝';

  @override
  String get cannotGetDownloadPath => '无法获取下载路径';

  @override
  String get checkUpdateFailed => '检查更新失败';

  @override
  String get installPermissionDenied => '安装权限被拒绝';

  @override
  String get failedToLoadContent => '加载内容失败';

  @override
  String get lastUpdated => '最后更新';

  @override
  String get termsAndConditions => '用户协议';

  @override
  String get agreementDialogContent => '请您务必审慎阅读、充分理解《用户协议》和《隐私政策》各条款，包括但不限于：为了向您提供产品和服务，我们需要收集您的设备信息、操作日志等个人信息。您可以在设置中查看、变更、删除个人信息并管理您的授权。';

  @override
  String get userAgreement => '用户协议';

  @override
  String get privacyPolicyText => '隐私政策';

  @override
  String get disagree => '不同意';

  @override
  String get exportSuccess => '导出成功';

  @override
  String get fileSavedTo => '文件已保存至：';

  @override
  String get fileContentPreview => '文件内容预览：';

  @override
  String get ok => '确定';

  @override
  String get openFileLocation => '打开文件位置';

  @override
  String get sharedFilterFile => '共享的过滤器文件';

  @override
  String get sharedFile => '共享的文件';

  @override
  String get cannotOpenFileOrItsDirectory => '无法打开文件或其目录';

  @override
  String get errorOpeningFileLocation => '打开文件位置时出错：';

  @override
  String get errorSharingFile => '共享文件时出错';

  @override
  String get cannotParseSelectedFile => '无法解析选定的文件。请确保它是有效的CSV格式，并包含频率和响应数据。';

  @override
  String get cannotExtractValidFrequencyAndResponseDataFromCSV => '无法从CSV中提取有效的频率和响应数据。';

  @override
  String get importSuccess => '导入成功';

  @override
  String get successfullyImportedFile => '成功导入文件';

  @override
  String get errorImportingFile => '导入文件时出错';

  @override
  String get noDataToExport => '没有数据可导出。';

  @override
  String get fileSavedToAppFolder => '文件已保存到应用文件夹：';

  @override
  String get errorSavingFile => '保存文件时出错：';

  @override
  String get saveMergedFilterFile => '保存合并的过滤器文件';

  @override
  String get startUsing => '开始使用';

  @override
  String get nextPage => '下一页';

  @override
  String get background => '背景';

  @override
  String get confirmDelete => '确认删除';

  @override
  String get confirmDeleteHint => '确认要删除此背景图片吗？';

  @override
  String get opacity => '不透明度';

  @override
  String get blur => '模糊度';

  @override
  String get selectFromAlbum => '从相册选择';

  @override
  String get takePhoto => '拍照';

  @override
  String get addBackground => '添加背景';

  @override
  String get deleteBackgroundFailed => '删除背景图片失败';

  @override
  String get saveBackgroundFailed => '保存背景图片失败';

  @override
  String get errorExportingFile => '导出文件时出错';

  @override
  String get skip => '跳过';

  @override
  String get version => '版本';

  @override
  String get copyright => ' 2025 拓品科技 版权所有';

  @override
  String get newFirmwareAvailable => '发现新固件';

  @override
  String get tip => '提示';

  @override
  String get firmwareUpgrade => '固件升级';

  @override
  String get firmwareUpgrading => '正在升级固件';

  @override
  String get firmwareUpgradeSuccess => '固件升级成功';

  @override
  String get firmwareUpgradeFailed => '固件升级失败';

  @override
  String get firmwareUpgradeConfirm => '确定要升级固件吗？';

  @override
  String get firmwareUpgradeWarning => '升级过程中请勿断开设备连接';

  @override
  String get firmwareSize => '固件大小';

  @override
  String get firmwareDescription => '更新说明';

  @override
  String get firmwareForceUpdate => '强制更新';

  @override
  String get firmwareDownloading => '正在下载固件';

  @override
  String get firmwareInstalling => '正在安装固件';

  @override
  String get settingResetFail => '重置失败';

  @override
  String get firmwareUpdateTitle => '固件升级';

  @override
  String get firmwareUpdateNewFirmwareFound => '发现新固件';

  @override
  String get firmwareUpdateFirmwareName => '固件名称';

  @override
  String get firmwareUpdateVersion => '版本号';

  @override
  String get firmwareUpdateDeviceModel => '设备型号';

  @override
  String get firmwareUpdateFileSize => '文件大小';

  @override
  String get firmwareUpdateDescription => '更新说明';

  @override
  String get firmwareUpdateMandatoryUpdateNote => '* 此版本为必要更新';

  @override
  String get firmwareUpdateStatus => '升级状态';

  @override
  String get firmwareUpdateDownloading => '正在下载';

  @override
  String get firmwareUpdateUpgrading => '正在升级';

  @override
  String get firmwareUpdateDoNotDisconnect => '请勿断开连接或关闭设备';

  @override
  String get firmwareUpdateReadyToUpdate => '准备升级';

  @override
  String get firmwareUpdateCancel => '取消升级';

  @override
  String get firmwareUpdateStart => '开始升级';

  @override
  String get firmwareUpdateLatestVersion => '当前已是最新版本';

  @override
  String get firmwareUpdateNoNeed => '无需升级固件';

  @override
  String get firmwareUpdateBack => '返回';

  @override
  String get firmwareUpdateSuccessTitle => '升级成功';

  @override
  String get firmwareUpdateSuccessMessage => '设备固件已更新至最新版本';

  @override
  String get firmwareUpdateSuccessRebootMessage => '升级成功，设备正在重启...';

  @override
  String get firmwareUpdateDownloadingTitle => '下载中';

  @override
  String get firmwareUpdateDownloadingMessage => '正在下载固件文件...';

  @override
  String get firmwareUpdateErrorTitle => '错误';

  @override
  String get firmwareUpdateDownloadFailed => '固件下载失败';

  @override
  String get firmwareUpdateUpgradingTitle => '升级中';

  @override
  String get firmwareUpdateUpgradingMessage => '开始升级固件...';

  @override
  String get firmwareUpdateSetDeviceFailed => '设置设备失败';

  @override
  String firmwareUpdateErrorDuringUpdate(String error) {
    return '升级过程出错: $error';
  }

  @override
  String get firmwareUpdateCancelledTitle => '已取消';

  @override
  String get firmwareUpdateCancelledMessage => '固件升级已取消';

  @override
  String get commonConfirm => '确认';

  @override
  String get checkFirmwareUpdate => '检查固件更新';

  @override
  String get deviceRebootTitle => '设备重启';

  @override
  String get deviceRebootMessage => '设备正在重新启动，请稍候...';

  @override
  String get infoTitle => '提示信息';

  @override
  String errorOpeningFile(String error) {
    return '打开文件错误: $error';
  }

  @override
  String get fileExported => '文件已导出';

  @override
  String get shareFile => '分享文件';

  @override
  String eqWavePainterStarted(String size) {
    return 'EQ波形绘制器已启动。尺寸: $size';
  }

  @override
  String targetRawDataPrepared(int count) {
    return '目标原始数据准备完成(已插值): $count 个点';
  }

  @override
  String sourceFRRawDataPrepared(int count) {
    return '源频响原始数据准备完成(已插值): $count 个点';
  }

  @override
  String combinedRawDataPrepared(int count) {
    return '组合原始数据准备完成: $count 个点';
  }

  @override
  String individualBandsRawDataPrepared(int bandCount, int pointCount) {
    return '单个波段原始数据准备完成: $bandCount 个波段, 总点数: $pointCount';
  }

  @override
  String filteredFRRawDataPrepared(int count) {
    return '滤波后频响原始数据准备完成(基于插值后的源): $count 个点';
  }

  @override
  String dynamicDbRangeCalculated(String min, String max) {
    return '动态dB范围计算完成: 最小值=$min, 最大值=$max';
  }

  @override
  String get coordinateConverterCreated => '坐标转换器已创建，并调整了图表高度';

  @override
  String get gridAndLabelsDrawn => '网格和标签已绘制';

  @override
  String get drawingTargetCurve => '正在绘制目标曲线...';

  @override
  String get drawingSourceFRCurve => '正在绘制源频响曲线...';

  @override
  String get drawingIndividualBands => '正在绘制单个波段...';

  @override
  String get drawingCombinedCurve => '正在绘制组合曲线...';

  @override
  String get drawingFilteredFRCurve => '正在绘制滤波后频响曲线...';

  @override
  String get drawingFlatLine => '正在绘制平直线...';

  @override
  String get eqWavePainterFinished => 'EQ波形绘制器完成';

  @override
  String finalDynamicDbRange(String min, String max) {
    return '最终动态dB范围: 最小值=$min, 最大值=$max';
  }

  @override
  String get peqSettings => 'PEQ设置';

  @override
  String get importExport => '导入导出';

  @override
  String get configManagement => '配置管理';

  @override
  String get addConfig => '添加配置';

  @override
  String get configName => '配置名称';

  @override
  String get description => '描述 (可选)';

  @override
  String get configNameHint => '例如：低音增强，清晰人声等';

  @override
  String get configDescriptionHint => '简要描述这个配置的用途';

  @override
  String get add => '添加';

  @override
  String get addNewConfig => '添加新配置';

  @override
  String get importTarget => '导入目标';

  @override
  String get importSourceFR => '导入源频响';

  @override
  String get exportCombinedFilter => '导出合并滤波';

  @override
  String get targetFR => '目标频响';

  @override
  String get editConfig => '编辑配置';

  @override
  String get deleteConfig => '删除配置';

  @override
  String deleteConfigConfirmation(Object name) {
    return '确定要删除配置 \"$name\" 吗？此操作不可撤销。';
  }

  @override
  String get deleteTargetFile => '删除目标文件';

  @override
  String get deleteSourceFRFile => '删除源频响文件';

  @override
  String deleteFileConfirmation(Object name) {
    return '确定要删除文件 \"$name\" 吗？此操作不可撤销。';
  }

  @override
  String get noConfigsMessage => '还没有配置，点击\"+\"创建一个新配置';

  @override
  String get devicePowerOff => '设备已关机';

  @override
  String get devicePowerOffHint => '开启电源后方可控制设备';

  @override
  String get powerOn => '开机';

  @override
  String get inputOptical1 => '光纤1';

  @override
  String get inputOptical2 => '光纤2';

  @override
  String get inputCoaxial1 => '同轴1';

  @override
  String get inputCoaxial2 => '同轴2';

  @override
  String get inputAes => 'AES';

  @override
  String get inputIis => 'IIS';

  @override
  String get outputDac => 'DAC';

  @override
  String get outputPreamp => '前级';

  @override
  String get outputAll => '全部';

  @override
  String get auto => '自动';

  @override
  String get enabled => '启用';

  @override
  String get standard => '标准';

  @override
  String get inverted => '反相';

  @override
  String get swapped => '交换';

  @override
  String get displaySignal => '信号';

  @override
  String get display12V => '12V';

  @override
  String get displayOff => '关闭';

  @override
  String get usbTypeC => 'Type-C';

  @override
  String get usbTypeB => 'Type-B';

  @override
  String get powerTriggerOff => '关闭';

  @override
  String get powerTrigger12V => '12V';

  @override
  String get multiFunctionKeyOutputSelect => '输出选择';

  @override
  String get multiFunctionKeyScreenOff => '息屏';

  @override
  String get usbSelect => 'USB选择';

  @override
  String get usbDsdPassthrough => 'USB DSD直通';

  @override
  String get iisPhase => 'IIS相位';

  @override
  String get iisDsdChannel => 'IIS DSD通道';
}

/// The translations for Chinese, using the Han script (`zh_Hant`).
class AppLocalizationsZhHant extends AppLocalizationsZh {
  AppLocalizationsZhHant(): super('zh_Hant');

  @override
  String get appName => '拓品之家';

  @override
  String get homeTitle => '我的產品';

  @override
  String get homeAddHint => '添加產品，帶來更多更好的操作體驗';

  @override
  String get addDevice => '添加設備';

  @override
  String get noDevice => '暫無設備';

  @override
  String get addDeviceHint => '添加產品，帶來更多更好的操作體驗';

  @override
  String get foundDevice => '發現設備';

  @override
  String get devices => '設備';

  @override
  String get loginClick => '點擊登入';

  @override
  String get about => '關於';

  @override
  String get quickStart => '快速入門';

  @override
  String get feedback => '意見反饋';

  @override
  String get customBackground => '自定義背景';

  @override
  String get logout => '退出登入';

  @override
  String get registerTitle => '註冊';

  @override
  String get phone => '手機號';

  @override
  String get phoneHint => '請輸入手機號';

  @override
  String get verifyCode => '驗證碼';

  @override
  String get getSmsVerifyCode => '獲取簡訊驗證碼';

  @override
  String get agreement => '我已閱讀並同意';

  @override
  String get agreementLink => '《用戶協議》';

  @override
  String get agree => '同意';

  @override
  String get privacyPolicy => '《隱私政策》';

  @override
  String get register => '註冊';

  @override
  String get login => '登入';

  @override
  String get loginTitle => '登入';

  @override
  String get loginPhoneHint => '請輸入手機號';

  @override
  String get loginVerifyCode => '驗證碼';

  @override
  String get loginGetSmsVerifyCode => '獲取簡訊驗證碼';

  @override
  String get loginPassword => '密碼登入';

  @override
  String get loginForgetPassword => '忘記密碼';

  @override
  String get loginFail => '登入失敗';

  @override
  String get passwordError => '密碼錯誤';

  @override
  String get phoneNotRegistered => '手機號未註冊';

  @override
  String get noAccount => '還沒有帳號?';

  @override
  String get registerNow => '立即註冊';

  @override
  String get tips => '提示';

  @override
  String get graphicCodeHint => '請輸入圖形驗證碼';

  @override
  String get agreeToTermsHint => '請同意用戶協議和隱私政策';

  @override
  String get verificationCodeHint => '請輸入驗證碼';

  @override
  String get usernameHint => '請輸入用戶名';

  @override
  String get passwordHint => '請輸入密碼';

  @override
  String get confirmPasswordHint => '請再次輸入密碼';

  @override
  String get passwordMismatch => '兩次輸入密碼不一致';

  @override
  String get registerSuccess => '註冊成功';

  @override
  String get registerSuccessHint => '請使用手機號和密碼登入';

  @override
  String get registerFailed => '註冊失敗';

  @override
  String get getVerificationCode => '獲取簡訊驗證碼';

  @override
  String get verificationCodeSent => '驗證碼已發送至';

  @override
  String get next => '下一步';

  @override
  String get secondResend => '秒後重新發送';

  @override
  String get settingAccount => '帳號設定';

  @override
  String get registerComplete => '完成註冊';

  @override
  String get username => '用戶名';

  @override
  String get password => '密碼';

  @override
  String get confirmPassword => '確認密碼';

  @override
  String get inputError => '輸入有誤';

  @override
  String get inputCannotBeEmpty => '輸入不能為空';

  @override
  String get invalidCaptcha => '驗證碼錯誤';

  @override
  String get forgetPassword => '忘記密碼';

  @override
  String get forgetPasswordTitle => '忘記密碼';

  @override
  String get forgetPasswordPhoneTitle => '請輸入手機號';

  @override
  String get forgetPasswordPhoneHint => '先通過您已綁定的手機號獲取驗證碼，再進行下一步操作';

  @override
  String get forgetPasswordVerifyCode => '驗證碼';

  @override
  String get forgetPasswordGetSmsVerifyCode => '獲取簡訊驗證碼';

  @override
  String get forgetPasswordNext => '下一步';

  @override
  String get forgetPasswordNewPassword => '新密碼';

  @override
  String get forgetPasswordNewPasswordHint => '請輸入新密碼';

  @override
  String get forgetPasswordConfirmPassword => '確認密碼';

  @override
  String get forgetPasswordConfirmPasswordHint => '請再次輸入新密碼';

  @override
  String get forgetPasswordReset => '重置密碼';

  @override
  String get forgetPasswordSuccess => '重置密碼成功';

  @override
  String get forgetPasswordSuccessHint => '請使用新密碼登入';

  @override
  String get forgetPasswordSuccessBack => '返回登入';

  @override
  String get forgetPasswordSuccessBackHome => '返回首頁';

  @override
  String get forgetPasswordSuccessBackLogin => '返回登入';

  @override
  String get forgetPasswordSuccessBackRegister => '返回註冊';

  @override
  String get forgetPasswordSuccessBackForgetPassword => '返回忘記密碼';

  @override
  String get aboutTitle => '關於';

  @override
  String get aboutVersion => '版本號';

  @override
  String get aboutVersionHint => '1.0.0';

  @override
  String get aboutUpdate => '檢查更新';

  @override
  String get aboutUpdateHint => '當前已是最新版本';

  @override
  String get aboutUpdateSuccess => '更新成功';

  @override
  String get feedbackTitle => '意見反饋';

  @override
  String get feedbackHint => '請留下您的寶貴意見，我們將不斷優化產品';

  @override
  String get feedbackContact => '聯絡方式';

  @override
  String get feedbackContactHint => '請輸入您的聯絡方式';

  @override
  String get feedbackContentHint => '請輸入您的反饋內容';

  @override
  String get feedbackImage => '上傳圖片';

  @override
  String get feedbackImageHint => '最多上傳5張圖片';

  @override
  String get feedbackDevice => '設備型號';

  @override
  String get feedbackDeviceHint => '請選擇您的設備型號';

  @override
  String get feedbackSubmit => '提交';

  @override
  String get feedbackSuccess => '提交成功';

  @override
  String get feedbackSuccessHint => '感謝您的反饋，我們將儘快處理';

  @override
  String get feedbackType => '反饋類型';

  @override
  String get feedbackTypeRequired => '請選擇反饋類型';

  @override
  String get feedbackError => '提交失敗，請稍後重試';

  @override
  String get error => '錯誤';

  @override
  String get logoutTitle => '退出登入';

  @override
  String get logoutHint => '確定要退出登入嗎？';

  @override
  String get cancel => '取消';

  @override
  String get confirm => '確定';

  @override
  String get deviceNotFoundTitle => '未找到您的設備？';

  @override
  String get deviceNotFoundHint => '1、請保持設備處於開機狀態\n2、請保持設備與手機藍牙正常連接\n3、完成上述步驟，點擊重新整理按鈕重新搜索。\n4、部分手機系統可能需要開啟定位功能後才能進行藍牙BLE連接，您可以嘗試開啟定位功能後再次重新整理';

  @override
  String get operateDescription => '操作說明';

  @override
  String get blueHeadAmp => '藍牙耳放';

  @override
  String get decoderAmp => '解碼耳放';

  @override
  String get headset => '耳機';

  @override
  String get bluetoothHeadphoneAmplifier => '藍牙及耳機';

  @override
  String get player => '播放器';

  @override
  String get inputLinker => '手動輸入Linker控制IP';

  @override
  String get connect => '已連接';

  @override
  String get disconnected => '已斷開';

  @override
  String get connecting => '正在連接';

  @override
  String get disconnecting => '正在斷開';

  @override
  String get disconnect => '未連接';

  @override
  String get personalCenter => '個人中心';

  @override
  String get editPersonalInfo => '編輯個人資訊';

  @override
  String get accountSecurity => '帳號安全設定';

  @override
  String get avatar => '頭像';

  @override
  String get gender => '性別';

  @override
  String get secret => '保密';

  @override
  String get male => '男';

  @override
  String get female => '女';

  @override
  String get birthday => '生日';

  @override
  String get signature => '個性簽名';

  @override
  String get region => '地區';

  @override
  String get nickname => '暱稱';

  @override
  String get save => '儲存';

  @override
  String get success => '成功';

  @override
  String get updateSuccess => '更新成功';

  @override
  String get userNotFound => '用戶不存在';

  @override
  String get loginPasswordModify => '登入密碼修改';

  @override
  String get accountAndBinding => '帳號與綁定設定';

  @override
  String get modifyPhone => '修改手機號';

  @override
  String get cancelAccount => '註銷帳號';

  @override
  String get modifyPassword => '修改密碼';

  @override
  String get oldPassword => '舊密碼';

  @override
  String get newPassword => '新密碼';

  @override
  String get confirmNewPassword => '確認新密碼';

  @override
  String get modifyPasswordSuccess => '修改密碼成功';

  @override
  String get informationIncomplete => '資訊填寫不完整';

  @override
  String get oldPasswordError => '舊密碼錯誤';

  @override
  String get newPasswordAndConfirmPasswordNotConsistent => '新密碼與確認密碼不一致';

  @override
  String get oldPasswordAndNewPasswordCannotBeTheSame => '舊密碼與新密碼不能相同';

  @override
  String get newPasswordCannotBeTheSameAsTheOldPassword => '新密碼不能與舊密碼相同';

  @override
  String get emailBinding => '郵箱綁定';

  @override
  String get notBound => '未綁定';

  @override
  String get bound => '已綁定';

  @override
  String get nowEmailVerification => '現在郵箱驗證';

  @override
  String get emailVerificationPrompt => '請輸入你需要綁定的郵箱，後續可以進行郵箱改綁和郵箱密碼找回';

  @override
  String get email => '郵箱';

  @override
  String get sendVerificationCode => '發送驗證碼';

  @override
  String get smsVerificationCode => '簡訊驗證碼';

  @override
  String get verificationCodeHasBeenSent => '驗證碼已發送';

  @override
  String get accountBindingSuccess => '帳號綁定成功';

  @override
  String get emailEmpty => '郵箱不能為空';

  @override
  String get invalidEmail => '郵箱格式不正確';

  @override
  String get emailBindSuccess => '郵箱綁定成功';

  @override
  String get unbindEmail => '解綁郵箱';

  @override
  String get unbindEmailConfirm => '確定要解綁郵箱嗎？';

  @override
  String get emailUnbindSuccess => '郵箱解綁成功';

  @override
  String get boundEmail => '已綁定郵箱';

  @override
  String get unbind => '解綁';

  @override
  String get bind => '綁定';

  @override
  String get getEmailVerificationCode => '獲取郵箱驗證碼';

  @override
  String get bindNewPhone => '綁定新手機';

  @override
  String get bindNewPhonePrompt => '請輸入您的新手機號，點擊獲取驗證碼完成驗證';

  @override
  String get bindNewPhoneSuccess => '綁定新手機成功';

  @override
  String get bindNewPhoneFailure => '綁定新手機失敗';

  @override
  String get bindNewPhoneFailurePrompt => '綁定新手機失敗，請稍後重試';

  @override
  String get will => '將';

  @override
  String get accountWillBeCancelled => '所綁定的帳號註銷';

  @override
  String get cancellationInstructions => '注意：註銷帳號僅支援帳號本人操作，且註銷後不支援恢復使用，請您審慎操作。\n正式註銷後，您將無法再使用本帳號，也無法找回帳號中以及帳號相關的任何內容資訊，包括但不限於：\n\n1、該帳號下的個人資訊（包括但不限於頭像、暱稱、綁定產品等）\n2、該帳號下的各類權益（包括但不限於MQA會員、論壇積分等）\n\n3、該帳號下的各類權益（包括但不限於播放清單等）\n\n4、其他應帳號註銷所（可能）產生的結果';

  @override
  String get cancellation => '註銷';

  @override
  String get confirmCancellation => '確定要註銷帳號嗎？';

  @override
  String get cancellationSuccess => '註銷成功';

  @override
  String get cancellationFailure => '註銷失敗';

  @override
  String get exitApp => '退出應用';

  @override
  String get exitAppPrompt => '確定要退出應用嗎？';

  @override
  String get phoneUpdateSuccess => '手機號修改成功';

  @override
  String get phoneEmpty => '手機號不能為空';

  @override
  String get verificationCodeEmpty => '驗證碼不能為空';

  @override
  String get samePhoneNumber => '新手機號與原手機號相同';

  @override
  String get verifyOldPhone => '驗證原手機號';

  @override
  String get setNewPhone => '設定新手機號';

  @override
  String get oldPhone => '原手機號';

  @override
  String get newPhone => '新手機號';

  @override
  String get passwordTooShort => '密碼長度不能少於6位';

  @override
  String get passwordEmpty => '密碼不能為空';

  @override
  String get passwordUpdateSuccess => '密碼修改成功';

  @override
  String get passwordUpdateSuccessRelogin => '密碼修改成功，請重新登入';

  @override
  String get scanning => '正在掃描';

  @override
  String get scanningDevicesHint => '正在掃描附近的藍牙設備...';

  @override
  String get startScanningHint => '開始掃描';

  @override
  String get manualInputIP => '手動輸入IP';

  @override
  String get manualInputIPHint => '請輸入Linker控制IP';

  @override
  String get bluetoothAndDac => '藍牙及DAC';

  @override
  String get streamer => '播放器';

  @override
  String get pleaseInputIP => '請輸入IP';

  @override
  String get deviceNotFoundHint1 => '1、請確保設備處於開機狀態。';

  @override
  String get deviceNotFoundHint2 => '2、請保持設備與手機藍牙正常連接。';

  @override
  String get deviceNotFoundHint3 => '3、完成上述步驟，點擊重新整理按鈕重新搜索。';

  @override
  String get deviceNotFoundHint4 => '4、部分手機系統可能需要開啟定位功能後才能進行藍牙BLE連接，您可以嘗試開啟定位功能後再次重新整理。';

  @override
  String get invalidQRCode => '無效二維碼';

  @override
  String get scanQRCode => '掃描二維碼';

  @override
  String get scanQRCodeHint => '請掃描設備上的二維碼';

  @override
  String get scanQRCodeBottomHint => '請將二維碼放入框內，即可自動掃描';

  @override
  String get noDevicesFound => '未找到設備';

  @override
  String get discoveredDevices => '可連接產品';

  @override
  String get discoveredDevicesHint => '發現多個可連接產品';

  @override
  String get tapToConnect => '輕觸連接';

  @override
  String get availableDevices => '可用設備';

  @override
  String get languageCode => 'zh_Hant';

  @override
  String get deviceNotFound => '設備未找到';

  @override
  String get connected => '已連接';

  @override
  String get battery => '電量';

  @override
  String get tryAgain => '再試一次';

  @override
  String get volumeControl => '音量控制';

  @override
  String get unlock => '解鎖';

  @override
  String get lock => '鎖定';

  @override
  String get audioSettings => '音訊設定';

  @override
  String get displayView => '顯示介面';

  @override
  String get low => '低';

  @override
  String get middle => '中';

  @override
  String get high => '高';

  @override
  String get equalizer => '等化器';

  @override
  String get eqPreset => 'EQ預設';

  @override
  String get digitalFilter => '數位濾波器';

  @override
  String get channelBalance => '聲道平衡';

  @override
  String get dsdMode => 'DSD模式';

  @override
  String get systemSettings => '系統設定';

  @override
  String get displayBrightness => '顯示亮度';

  @override
  String get displayTimeout => '顯示超時';

  @override
  String get autoPowerOff => '自動關機';

  @override
  String get disabled => '禁用';

  @override
  String get ledIndicator => 'LED指示燈';

  @override
  String get connectToAccessSettings => '連接到訪問設定';

  @override
  String get connectionInfo => '連接資訊';

  @override
  String get codec => '編解碼器';

  @override
  String get deviceInfo => '設備資訊';

  @override
  String get firmwareVersion => '韌體版本';

  @override
  String get serialNumber => '序號';

  @override
  String get totalPlayTime => '總播放時間';

  @override
  String get deleteDevice => '刪除設備';

  @override
  String get deleteDeviceConfirm => '確定要刪除設備嗎？';

  @override
  String get delete => '刪除';

  @override
  String get preamplifier => '前級';

  @override
  String get decodeModeDac => 'DAC';

  @override
  String get displayNormal => '普通';

  @override
  String get displayVu => 'VU表';

  @override
  String get displayFft => '頻譜';

  @override
  String get inputUsb => 'USB';

  @override
  String get inputOptical => '光纖';

  @override
  String get inputCoaxial => '同軸';

  @override
  String get inputBluetooth => '藍牙';

  @override
  String get languageZh => '中文';

  @override
  String get languageEn => '英文';

  @override
  String get headphoneGainHigh => '高增益';

  @override
  String get headphoneGainLow => '低增益';

  @override
  String get multiFunctionKeyInputSelect => '輸入選擇';

  @override
  String get multiFunctionKeyLineOutSelect => '線路輸出選擇';

  @override
  String get multiFunctionKeyHeadphoneOutSelect => '耳放輸出選擇';

  @override
  String get multiFunctionKeyHomeSelect => '主頁選擇';

  @override
  String get multiFunctionKeyBrightnessSelect => '亮度選擇';

  @override
  String get multiFunctionKeySleep => '息屏';

  @override
  String get multiFunctionKeyPcmFilterSelect => 'PCM濾波器選擇';

  @override
  String get multiFunctionKeyMute => '靜音';

  @override
  String get multiFunctionKeyPeqSelect => 'PEQ選擇';

  @override
  String get outputClose => '關閉';

  @override
  String get outputSingleEnded => 'RCA';

  @override
  String get outputBalanced => 'XLR';

  @override
  String get outputSingleEndedAndBalanced => 'RCA+XLR';

  @override
  String get powerTriggerSignal => '信號';

  @override
  String get powerTriggerVoltage => '12V';

  @override
  String get powerTriggerClose => '關閉';

  @override
  String get screenBrightnessHigh => '高';

  @override
  String get screenBrightnessMedium => '中';

  @override
  String get screenBrightnessLow => '低';

  @override
  String get screenBrightnessAuto => '自動';

  @override
  String get themeAurora => '極光';

  @override
  String get themeOrange => '橙色';

  @override
  String get themePeru => '秘魯色';

  @override
  String get themeGreen => '豆綠色';

  @override
  String get themeKhaki => '深卡其色';

  @override
  String get themeRose => '玫瑰棕色';

  @override
  String get themeBlue => '藍色';

  @override
  String get themePurple => '幻紫色';

  @override
  String get themeWhite => '白色';

  @override
  String get darkMode => '深色模式';

  @override
  String get lightMode => '淺色模式';

  @override
  String get darkModeDescription => '當前使用深色主題';

  @override
  String get lightModeDescription => '當前使用淺色主題';

  @override
  String get usbTypeUac2 => 'UAC 2.0';

  @override
  String get usbTypeUac1 => 'UAC 1.0';

  @override
  String get deviceTypeBluetooth => '藍牙';

  @override
  String get deviceTypeDac => 'DAC';

  @override
  String get deviceTypeHeadphone => '耳放';

  @override
  String get deviceTypePlayer => '播放器';

  @override
  String get name => '名稱';

  @override
  String get volume => '音量';

  @override
  String get headphoneOutput => '耳放輸出';

  @override
  String get headphoneGain => '耳放增益';

  @override
  String get inputSelect => '輸入選擇';

  @override
  String get outputSelect => '輸出選擇';

  @override
  String get advanced => '進階';

  @override
  String get advancedSettings => '進階設定';

  @override
  String get editName => '修改名稱';

  @override
  String get enterNewName => '請輸入新名稱';

  @override
  String get setting => '設定';

  @override
  String get peq => '等化器';

  @override
  String get guide => '操作指南';

  @override
  String get theme => '主題';

  @override
  String get powerTrigger => '開關機觸發';

  @override
  String get audioBalance => '聲道平衡';

  @override
  String get filter => '濾波器';

  @override
  String get decodeMode => '解碼模式';

  @override
  String get audioMonitoring => '音訊藍牙';

  @override
  String get bluetoothAptx => '藍牙APTX';

  @override
  String get relay => '遙控器';

  @override
  String get multiFunctionKey => '自定義多功能按鍵';

  @override
  String get usbMode => 'USB';

  @override
  String get screenBrightness => '螢幕亮度';

  @override
  String get language => '語言';

  @override
  String get resetSettings => '恢復進階設定';

  @override
  String get restoreFactorySettings => '恢復出廠設定';

  @override
  String get channel => '聲道';

  @override
  String get resetSettingsConfirmation => '確定要恢復進階設定嗎？';

  @override
  String get restoreFactorySettingsConfirmation => '確定要恢復出廠設定嗎？';

  @override
  String get import => '導入';

  @override
  String get export => '導出';

  @override
  String get target => '目標';

  @override
  String get sourceFR => '源FR';

  @override
  String get eachFilter => '每個濾波';

  @override
  String get combinedFilter => '合併濾波';

  @override
  String get filteredFR => '濾波後頻響';

  @override
  String get raw => '原始';

  @override
  String get compensated => '補償';

  @override
  String get preAmplification => '前級';

  @override
  String get gain => '增益';

  @override
  String get peakingFilter => '峰值濾波';

  @override
  String get lowPassFilter => '低通濾波';

  @override
  String get highPassFilter => '高通濾波';

  @override
  String get lowShelfFilter => '低濾搁架';

  @override
  String get highShelfFilter => '高濾搁架';

  @override
  String get centerFrequency => '中心頻率';

  @override
  String get connerFrequency => '拐點頻率';

  @override
  String get q => 'Q值';

  @override
  String get frequency => '頻率';

  @override
  String get type => '類型';

  @override
  String get addFilter => '添加濾波';

  @override
  String get mode => '模式';

  @override
  String get import_target => '導入目標曲線';

  @override
  String get import_sourceFR => '導入源頻響曲線';

  @override
  String get selectDataFile => '選擇數據文件';

  @override
  String get feedbackContent => '反饋內容';

  @override
  String get problemType => '問題類型';

  @override
  String get selectDeviceType => '選擇設備類型';

  @override
  String get device => '設備';

  @override
  String get addPicture => '添加圖片';

  @override
  String get contact => '聯繫我們';

  @override
  String get feedbackTypeFeatureSuggestion => '功能建議';

  @override
  String get feedbackTypeBugReport => '錯誤報告';

  @override
  String get feedbackTypeUIImprovement => 'UI優化';

  @override
  String get feedbackTypeOther => '其他';

  @override
  String get checkUpdate => '檢查更新';

  @override
  String get checking => '檢查中...';

  @override
  String get alreadyLatestVersion => '已是最新版本';

  @override
  String get foundNewVersion => '發現新版本';

  @override
  String get currentVersion => '當前版本';

  @override
  String get newVersion => '新版本';

  @override
  String get updateContent => '更新內容';

  @override
  String get later => '以後再說';

  @override
  String get updateNow => '立即更新';

  @override
  String get downloading => '下載中...';

  @override
  String get downloadCompleted => '下載完成';

  @override
  String get downloadFailed => '下載失敗';

  @override
  String get storagePermissionDenied => '存儲權限被拒絕';

  @override
  String get cannotGetDownloadPath => '無法獲取下載路徑';

  @override
  String get checkUpdateFailed => '檢查更新失敗';

  @override
  String get installPermissionDenied => '安裝權限被拒絕';

  @override
  String get failedToLoadContent => '加載內容失敗';

  @override
  String get lastUpdated => '最後更新';

  @override
  String get termsAndConditions => '條款與條件';

  @override
  String get agreementDialogContent => '請閱讀並同意《用戶協議》和《隱私政策》';

  @override
  String get userAgreement => '用戶協議';

  @override
  String get privacyPolicyText => '隱私政策';

  @override
  String get disagree => '不同意';

  @override
  String get exportSuccess => '匯出成功';

  @override
  String get fileSavedTo => '檔案已儲存至：';

  @override
  String get fileContentPreview => '檔案內容預覽：';

  @override
  String get ok => '確定';

  @override
  String get openFileLocation => '開啟檔案位置';

  @override
  String get sharedFilterFile => '共享的過濾器檔案';

  @override
  String get sharedFile => '共享的檔案';

  @override
  String get cannotOpenFileOrItsDirectory => '無法開啟檔案或其目錄';

  @override
  String get errorOpeningFileLocation => '開啟檔案位置時出錯：';

  @override
  String get errorSharingFile => '共享檔案時出錯';

  @override
  String get cannotParseSelectedFile => '無法解析所選檔案。請確保它是有效的CSV格式，並包含頻率和響應數據。';

  @override
  String get cannotExtractValidFrequencyAndResponseDataFromCSV => '無法從CSV中提取有效的頻率和響應數據。';

  @override
  String get importSuccess => '匯入成功';

  @override
  String get successfullyImportedFile => '成功匯入檔案';

  @override
  String get errorImportingFile => '匯入檔案時出錯';

  @override
  String get noDataToExport => '沒有數據可匯出。';

  @override
  String get fileSavedToAppFolder => '檔案已儲存至應用程式資料夾：';

  @override
  String get errorSavingFile => '儲存檔案時出錯：';

  @override
  String get saveMergedFilterFile => '儲存合併的過濾器檔案';

  @override
  String get startUsing => '開始使用';

  @override
  String get nextPage => '下一頁';

  @override
  String get background => '背景';

  @override
  String get confirmDelete => '確認刪除';

  @override
  String get confirmDeleteHint => '確定要刪除此背景圖片嗎？';

  @override
  String get opacity => '不透明度';

  @override
  String get blur => '模糊度';

  @override
  String get selectFromAlbum => '從相簿選擇';

  @override
  String get takePhoto => '拍照';

  @override
  String get addBackground => '添加背景';

  @override
  String get deleteBackgroundFailed => '刪除背景失敗';

  @override
  String get saveBackgroundFailed => '保存背景失敗';

  @override
  String get errorExportingFile => '匯出檔案時出錯';

  @override
  String get skip => '跳過';

  @override
  String get version => '版本';

  @override
  String get copyright => ' 2025 拓品科技 版權所有';

  @override
  String get newFirmwareAvailable => '發現新固件';

  @override
  String get tip => '提示';

  @override
  String get firmwareUpgrade => '固件升級';

  @override
  String get firmwareUpgrading => '固件升級中';

  @override
  String get firmwareUpgradeSuccess => '固件升級成功';

  @override
  String get firmwareUpgradeFailed => '固件升級失敗';

  @override
  String get firmwareUpgradeConfirm => '確定要升級固件嗎？';

  @override
  String get firmwareUpgradeWarning => '請注意，升級後將覆蓋所有設定。';

  @override
  String get firmwareSize => '固件大小';

  @override
  String get firmwareDescription => '固件描述';

  @override
  String get firmwareForceUpdate => '強制更新';

  @override
  String get firmwareDownloading => '正在下載固件';

  @override
  String get firmwareInstalling => '正在安裝韌體';

  @override
  String get settingResetFail => '重設失敗';

  @override
  String get firmwareUpdateTitle => '韌體更新';

  @override
  String get firmwareUpdateNewFirmwareFound => '發現新韌體';

  @override
  String get firmwareUpdateFirmwareName => '韌體名稱';

  @override
  String get firmwareUpdateVersion => '版本號';

  @override
  String get firmwareUpdateDeviceModel => '設備型號';

  @override
  String get firmwareUpdateFileSize => '檔案大小';

  @override
  String get firmwareUpdateDescription => '更新說明';

  @override
  String get firmwareUpdateMandatoryUpdateNote => '* 此版本為必要更新';

  @override
  String get firmwareUpdateStatus => '更新狀態';

  @override
  String get firmwareUpdateDownloading => '正在下載';

  @override
  String get firmwareUpdateUpgrading => '正在更新';

  @override
  String get firmwareUpdateDoNotDisconnect => '請勿斷開連線或關閉設備';

  @override
  String get firmwareUpdateReadyToUpdate => '準備更新';

  @override
  String get firmwareUpdateCancel => '取消更新';

  @override
  String get firmwareUpdateStart => '開始更新';

  @override
  String get firmwareUpdateLatestVersion => '目前已是最新版本';

  @override
  String get firmwareUpdateNoNeed => '無需更新韌體';

  @override
  String get firmwareUpdateBack => '返回';

  @override
  String get firmwareUpdateSuccessTitle => '更新成功';

  @override
  String get firmwareUpdateSuccessMessage => '設備韌體已更新至最新版本';

  @override
  String get firmwareUpdateSuccessRebootMessage => '更新成功，設備正在重新啟動...';

  @override
  String get firmwareUpdateDownloadingTitle => '下載中';

  @override
  String get firmwareUpdateDownloadingMessage => '正在下載韌體檔案...';

  @override
  String get firmwareUpdateErrorTitle => '錯誤';

  @override
  String get firmwareUpdateDownloadFailed => '韌體下載失敗';

  @override
  String get firmwareUpdateUpgradingTitle => '更新中';

  @override
  String get firmwareUpdateUpgradingMessage => '開始更新韌體...';

  @override
  String get firmwareUpdateSetDeviceFailed => '設定設備失敗';

  @override
  String firmwareUpdateErrorDuringUpdate(String error) {
    return '更新過程中發生錯誤: $error';
  }

  @override
  String get firmwareUpdateCancelledTitle => '已取消';

  @override
  String get firmwareUpdateCancelledMessage => '韌體更新已取消';

  @override
  String get commonConfirm => '確認';

  @override
  String get checkFirmwareUpdate => '檢查韌體更新';

  @override
  String get deviceRebootTitle => '設備重新啟動';

  @override
  String get deviceRebootMessage => '設備正在重新啟動，請稍候...';

  @override
  String get infoTitle => '提示訊息';

  @override
  String errorOpeningFile(String error) {
    return '開啟檔案錯誤: $error';
  }

  @override
  String get fileExported => '檔案已導出';

  @override
  String get shareFile => '分享檔案';

  @override
  String get peqSettings => 'PEQ設定';

  @override
  String get importExport => '匯入與匯出';

  @override
  String get configManagement => '配置管理';

  @override
  String get addConfig => '新增配置';

  @override
  String get configName => '配置名稱';

  @override
  String get description => '描述 (選填)';

  @override
  String get configNameHint => '例：低音增強、清晰人聲等';

  @override
  String get configDescriptionHint => '簡單描述此配置的用途';

  @override
  String get add => '新增';

  @override
  String get addNewConfig => '新增配置';

  @override
  String get importTarget => '匯入目標';

  @override
  String get importSourceFR => '匯入源FR';

  @override
  String get exportCombinedFilter => '匯出合併濾波器';

  @override
  String get targetFR => '目標FR';

  @override
  String get editConfig => '編輯配置';

  @override
  String get deleteConfig => '刪除配置';

  @override
  String deleteConfigConfirmation(Object name) {
    return '確定要刪除配置\"$name\"嗎？此操作無法撤銷。';
  }

  @override
  String get deleteTargetFile => '刪除目標檔案';

  @override
  String get deleteSourceFRFile => '刪除源FR檔案';

  @override
  String deleteFileConfirmation(Object name) {
    return '確定要刪除檔案\"$name\"嗎？此操作無法撤銷。';
  }

  @override
  String get noConfigsMessage => '尚無配置。請點擊\"+\"新增配置。';

  @override
  String get devicePowerOff => '設備已關機';

  @override
  String get devicePowerOffHint => '開啟電源後方可控制設備';

  @override
  String get powerOn => '開機';

  @override
  String get inputOptical1 => '光纜1';

  @override
  String get inputOptical2 => '光纜2';

  @override
  String get inputCoaxial1 => '同軸1';

  @override
  String get inputCoaxial2 => '同軸2';

  @override
  String get inputAes => 'AES';

  @override
  String get inputIis => 'IIS';

  @override
  String get outputDac => 'DAC';

  @override
  String get outputPreamp => '前級';

  @override
  String get outputAll => '全部';

  @override
  String get auto => '自動';

  @override
  String get enabled => '啟用';

  @override
  String get standard => '標準';

  @override
  String get inverted => '反相';

  @override
  String get swapped => '交換';

  @override
  String get displaySignal => '信號';

  @override
  String get display12V => '12V';

  @override
  String get displayOff => '關閉';

  @override
  String get usbTypeC => 'Type-C';

  @override
  String get usbTypeB => 'Type-B';

  @override
  String get powerTriggerOff => '關閉';

  @override
  String get powerTrigger12V => '12V';

  @override
  String get multiFunctionKeyOutputSelect => '輸出選擇';

  @override
  String get multiFunctionKeyScreenOff => '息屏';

  @override
  String get usbSelect => 'USB選擇';

  @override
  String get usbDsdPassthrough => 'USB DSD直通';

  @override
  String get iisPhase => 'IIS相位';

  @override
  String get iisDsdChannel => 'IIS DSD通道';
}
