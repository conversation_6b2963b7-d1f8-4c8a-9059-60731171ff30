import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_ja.dart';
import 'app_localizations_ko.dart';
import 'app_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('ja'),
    Locale('ko'),
    Locale('zh'),
    Locale.fromSubtags(languageCode: 'zh', scriptCode: 'Hant')
  ];

  /// No description provided for @appName.
  ///
  /// In en, this message translates to:
  /// **'Topping Home'**
  String get appName;

  /// No description provided for @homeTitle.
  ///
  /// In en, this message translates to:
  /// **'My Products'**
  String get homeTitle;

  /// No description provided for @homeAddHint.
  ///
  /// In en, this message translates to:
  /// **'Add devices for better experience'**
  String get homeAddHint;

  /// No description provided for @addDevice.
  ///
  /// In en, this message translates to:
  /// **'Add Device'**
  String get addDevice;

  /// No description provided for @noDevice.
  ///
  /// In en, this message translates to:
  /// **'No Device'**
  String get noDevice;

  /// No description provided for @addDeviceHint.
  ///
  /// In en, this message translates to:
  /// **'Click to add new device'**
  String get addDeviceHint;

  /// No description provided for @foundDevice.
  ///
  /// In en, this message translates to:
  /// **'Found Device'**
  String get foundDevice;

  /// No description provided for @devices.
  ///
  /// In en, this message translates to:
  /// **'Devices'**
  String get devices;

  /// No description provided for @loginClick.
  ///
  /// In en, this message translates to:
  /// **'Click to Login'**
  String get loginClick;

  /// No description provided for @about.
  ///
  /// In en, this message translates to:
  /// **'About'**
  String get about;

  /// No description provided for @quickStart.
  ///
  /// In en, this message translates to:
  /// **'Quick Start'**
  String get quickStart;

  /// No description provided for @feedback.
  ///
  /// In en, this message translates to:
  /// **'Feedback'**
  String get feedback;

  /// No description provided for @customBackground.
  ///
  /// In en, this message translates to:
  /// **'Custom Background'**
  String get customBackground;

  /// No description provided for @logout.
  ///
  /// In en, this message translates to:
  /// **'Log Out'**
  String get logout;

  /// No description provided for @registerTitle.
  ///
  /// In en, this message translates to:
  /// **'Register'**
  String get registerTitle;

  /// No description provided for @phone.
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get phone;

  /// No description provided for @phoneHint.
  ///
  /// In en, this message translates to:
  /// **'Please enter your phone number'**
  String get phoneHint;

  /// No description provided for @verifyCode.
  ///
  /// In en, this message translates to:
  /// **'Verification Code'**
  String get verifyCode;

  /// No description provided for @getSmsVerifyCode.
  ///
  /// In en, this message translates to:
  /// **'Get SMS Code'**
  String get getSmsVerifyCode;

  /// No description provided for @agreement.
  ///
  /// In en, this message translates to:
  /// **'I have read and agree to the'**
  String get agreement;

  /// No description provided for @agreementLink.
  ///
  /// In en, this message translates to:
  /// **'User Agreement'**
  String get agreementLink;

  /// No description provided for @agree.
  ///
  /// In en, this message translates to:
  /// **'Agree'**
  String get agree;

  /// No description provided for @privacyPolicy.
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicy;

  /// No description provided for @register.
  ///
  /// In en, this message translates to:
  /// **'Register'**
  String get register;

  /// No description provided for @login.
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get login;

  /// No description provided for @loginTitle.
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get loginTitle;

  /// No description provided for @loginPhoneHint.
  ///
  /// In en, this message translates to:
  /// **'Please enter your phone number'**
  String get loginPhoneHint;

  /// No description provided for @loginVerifyCode.
  ///
  /// In en, this message translates to:
  /// **'Verification Code'**
  String get loginVerifyCode;

  /// No description provided for @loginGetSmsVerifyCode.
  ///
  /// In en, this message translates to:
  /// **'Get SMS Code'**
  String get loginGetSmsVerifyCode;

  /// No description provided for @loginPassword.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get loginPassword;

  /// No description provided for @loginForgetPassword.
  ///
  /// In en, this message translates to:
  /// **'Forgot Password'**
  String get loginForgetPassword;

  /// No description provided for @loginFail.
  ///
  /// In en, this message translates to:
  /// **'Login Failed'**
  String get loginFail;

  /// No description provided for @passwordError.
  ///
  /// In en, this message translates to:
  /// **'Incorrect Password'**
  String get passwordError;

  /// No description provided for @phoneNotRegistered.
  ///
  /// In en, this message translates to:
  /// **'Phone Number Not Registered'**
  String get phoneNotRegistered;

  /// No description provided for @noAccount.
  ///
  /// In en, this message translates to:
  /// **'Don\'\'t have an account?'**
  String get noAccount;

  /// No description provided for @registerNow.
  ///
  /// In en, this message translates to:
  /// **'Register Now'**
  String get registerNow;

  /// No description provided for @tips.
  ///
  /// In en, this message translates to:
  /// **'Tips'**
  String get tips;

  /// No description provided for @graphicCodeHint.
  ///
  /// In en, this message translates to:
  /// **'Please enter the graphic code'**
  String get graphicCodeHint;

  /// No description provided for @agreeToTermsHint.
  ///
  /// In en, this message translates to:
  /// **'Please agree to the User Agreement and Privacy Policy'**
  String get agreeToTermsHint;

  /// No description provided for @verificationCodeHint.
  ///
  /// In en, this message translates to:
  /// **'Please enter the verification code'**
  String get verificationCodeHint;

  /// No description provided for @usernameHint.
  ///
  /// In en, this message translates to:
  /// **'Please enter your username'**
  String get usernameHint;

  /// No description provided for @passwordHint.
  ///
  /// In en, this message translates to:
  /// **'Please enter your password'**
  String get passwordHint;

  /// No description provided for @confirmPasswordHint.
  ///
  /// In en, this message translates to:
  /// **'Please re-enter your password'**
  String get confirmPasswordHint;

  /// No description provided for @passwordMismatch.
  ///
  /// In en, this message translates to:
  /// **'Passwords do not match'**
  String get passwordMismatch;

  /// No description provided for @registerSuccess.
  ///
  /// In en, this message translates to:
  /// **'Registration Successful'**
  String get registerSuccess;

  /// No description provided for @registerSuccessHint.
  ///
  /// In en, this message translates to:
  /// **'Please use your phone number and password to log in'**
  String get registerSuccessHint;

  /// No description provided for @registerFailed.
  ///
  /// In en, this message translates to:
  /// **'Registration Failed'**
  String get registerFailed;

  /// No description provided for @getVerificationCode.
  ///
  /// In en, this message translates to:
  /// **'Get SMS Code'**
  String get getVerificationCode;

  /// No description provided for @verificationCodeSent.
  ///
  /// In en, this message translates to:
  /// **'Verification code has been sent to'**
  String get verificationCodeSent;

  /// No description provided for @next.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// No description provided for @secondResend.
  ///
  /// In en, this message translates to:
  /// **'s to resend'**
  String get secondResend;

  /// No description provided for @settingAccount.
  ///
  /// In en, this message translates to:
  /// **'Account Settings'**
  String get settingAccount;

  /// No description provided for @registerComplete.
  ///
  /// In en, this message translates to:
  /// **'Registration Complete'**
  String get registerComplete;

  /// No description provided for @username.
  ///
  /// In en, this message translates to:
  /// **'Username'**
  String get username;

  /// No description provided for @password.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// No description provided for @confirmPassword.
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirmPassword;

  /// No description provided for @inputError.
  ///
  /// In en, this message translates to:
  /// **'Input Error'**
  String get inputError;

  /// No description provided for @inputCannotBeEmpty.
  ///
  /// In en, this message translates to:
  /// **'Input cannot be empty'**
  String get inputCannotBeEmpty;

  /// No description provided for @invalidCaptcha.
  ///
  /// In en, this message translates to:
  /// **'Invalid Captcha'**
  String get invalidCaptcha;

  /// No description provided for @forgetPassword.
  ///
  /// In en, this message translates to:
  /// **'Forgot Password'**
  String get forgetPassword;

  /// No description provided for @forgetPasswordTitle.
  ///
  /// In en, this message translates to:
  /// **'Forgot Password'**
  String get forgetPasswordTitle;

  /// No description provided for @forgetPasswordPhoneTitle.
  ///
  /// In en, this message translates to:
  /// **'Please enter your phone number'**
  String get forgetPasswordPhoneTitle;

  /// No description provided for @forgetPasswordPhoneHint.
  ///
  /// In en, this message translates to:
  /// **'Use the bound phone number to get a verification code, then proceed'**
  String get forgetPasswordPhoneHint;

  /// No description provided for @forgetPasswordVerifyCode.
  ///
  /// In en, this message translates to:
  /// **'Verification Code'**
  String get forgetPasswordVerifyCode;

  /// No description provided for @forgetPasswordGetSmsVerifyCode.
  ///
  /// In en, this message translates to:
  /// **'Get SMS Code'**
  String get forgetPasswordGetSmsVerifyCode;

  /// No description provided for @forgetPasswordNext.
  ///
  /// In en, this message translates to:
  /// **'Next Step'**
  String get forgetPasswordNext;

  /// No description provided for @forgetPasswordNewPassword.
  ///
  /// In en, this message translates to:
  /// **'New Password'**
  String get forgetPasswordNewPassword;

  /// No description provided for @forgetPasswordNewPasswordHint.
  ///
  /// In en, this message translates to:
  /// **'Please enter your new password'**
  String get forgetPasswordNewPasswordHint;

  /// No description provided for @forgetPasswordConfirmPassword.
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get forgetPasswordConfirmPassword;

  /// No description provided for @forgetPasswordConfirmPasswordHint.
  ///
  /// In en, this message translates to:
  /// **'Please re-enter your new password'**
  String get forgetPasswordConfirmPasswordHint;

  /// No description provided for @forgetPasswordReset.
  ///
  /// In en, this message translates to:
  /// **'Reset Password'**
  String get forgetPasswordReset;

  /// No description provided for @forgetPasswordSuccess.
  ///
  /// In en, this message translates to:
  /// **'Password Reset Successful'**
  String get forgetPasswordSuccess;

  /// No description provided for @forgetPasswordSuccessHint.
  ///
  /// In en, this message translates to:
  /// **'Please use the new password to log in'**
  String get forgetPasswordSuccessHint;

  /// No description provided for @forgetPasswordSuccessBack.
  ///
  /// In en, this message translates to:
  /// **'Back to Login'**
  String get forgetPasswordSuccessBack;

  /// No description provided for @forgetPasswordSuccessBackHome.
  ///
  /// In en, this message translates to:
  /// **'Back to Home'**
  String get forgetPasswordSuccessBackHome;

  /// No description provided for @forgetPasswordSuccessBackLogin.
  ///
  /// In en, this message translates to:
  /// **'Back to Login'**
  String get forgetPasswordSuccessBackLogin;

  /// No description provided for @forgetPasswordSuccessBackRegister.
  ///
  /// In en, this message translates to:
  /// **'Back to Register'**
  String get forgetPasswordSuccessBackRegister;

  /// No description provided for @forgetPasswordSuccessBackForgetPassword.
  ///
  /// In en, this message translates to:
  /// **'Back to Forgot Password'**
  String get forgetPasswordSuccessBackForgetPassword;

  /// No description provided for @aboutTitle.
  ///
  /// In en, this message translates to:
  /// **'About'**
  String get aboutTitle;

  /// No description provided for @aboutVersion.
  ///
  /// In en, this message translates to:
  /// **'Version'**
  String get aboutVersion;

  /// No description provided for @aboutVersionHint.
  ///
  /// In en, this message translates to:
  /// **'1.0.0'**
  String get aboutVersionHint;

  /// No description provided for @aboutUpdate.
  ///
  /// In en, this message translates to:
  /// **'Check for Updates'**
  String get aboutUpdate;

  /// No description provided for @aboutUpdateHint.
  ///
  /// In en, this message translates to:
  /// **'You have the latest version'**
  String get aboutUpdateHint;

  /// No description provided for @aboutUpdateSuccess.
  ///
  /// In en, this message translates to:
  /// **'Update Successful'**
  String get aboutUpdateSuccess;

  /// No description provided for @feedbackTitle.
  ///
  /// In en, this message translates to:
  /// **'Feedback'**
  String get feedbackTitle;

  /// No description provided for @feedbackHint.
  ///
  /// In en, this message translates to:
  /// **'Please leave your valuable comments. We will keep improving our products.'**
  String get feedbackHint;

  /// No description provided for @feedbackContact.
  ///
  /// In en, this message translates to:
  /// **'Contact Information'**
  String get feedbackContact;

  /// No description provided for @feedbackContactHint.
  ///
  /// In en, this message translates to:
  /// **'Please enter your contact information'**
  String get feedbackContactHint;

  /// No description provided for @feedbackContentHint.
  ///
  /// In en, this message translates to:
  /// **'Please enter your feedback'**
  String get feedbackContentHint;

  /// No description provided for @feedbackImage.
  ///
  /// In en, this message translates to:
  /// **'Upload Image'**
  String get feedbackImage;

  /// No description provided for @feedbackImageHint.
  ///
  /// In en, this message translates to:
  /// **'You can upload up to 5 images'**
  String get feedbackImageHint;

  /// No description provided for @feedbackDevice.
  ///
  /// In en, this message translates to:
  /// **'Device Model'**
  String get feedbackDevice;

  /// No description provided for @feedbackDeviceHint.
  ///
  /// In en, this message translates to:
  /// **'Please select your device model'**
  String get feedbackDeviceHint;

  /// No description provided for @feedbackSubmit.
  ///
  /// In en, this message translates to:
  /// **'Submit'**
  String get feedbackSubmit;

  /// No description provided for @feedbackSuccess.
  ///
  /// In en, this message translates to:
  /// **'Submission Successful'**
  String get feedbackSuccess;

  /// No description provided for @feedbackSuccessHint.
  ///
  /// In en, this message translates to:
  /// **'Thank you for your feedback. We will handle it as soon as possible'**
  String get feedbackSuccessHint;

  /// No description provided for @feedbackType.
  ///
  /// In en, this message translates to:
  /// **'Feedback Type'**
  String get feedbackType;

  /// No description provided for @feedbackTypeRequired.
  ///
  /// In en, this message translates to:
  /// **'Please select a feedback type'**
  String get feedbackTypeRequired;

  /// No description provided for @feedbackError.
  ///
  /// In en, this message translates to:
  /// **'Submission Failed, please try again later'**
  String get feedbackError;

  /// No description provided for @error.
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// No description provided for @logoutTitle.
  ///
  /// In en, this message translates to:
  /// **'Log Out'**
  String get logoutTitle;

  /// No description provided for @logoutHint.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to log out?'**
  String get logoutHint;

  /// Cancel button text
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @confirm.
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// No description provided for @deviceNotFoundTitle.
  ///
  /// In en, this message translates to:
  /// **'Device Not Found?'**
  String get deviceNotFoundTitle;

  /// No description provided for @deviceNotFoundHint.
  ///
  /// In en, this message translates to:
  /// **'1. Please ensure the device is powered on.\n2. Make sure the device is properly connected via Bluetooth.\n3. After completing the above steps, tap the refresh button to search again.\n4. Some phones may need location services enabled for BLE connections. Try enabling location services and refresh again.'**
  String get deviceNotFoundHint;

  /// No description provided for @operateDescription.
  ///
  /// In en, this message translates to:
  /// **'Operation Instructions'**
  String get operateDescription;

  /// No description provided for @blueHeadAmp.
  ///
  /// In en, this message translates to:
  /// **'Bluetooth Headphone Amplifier'**
  String get blueHeadAmp;

  /// No description provided for @decoderAmp.
  ///
  /// In en, this message translates to:
  /// **'Decoder Amplifier'**
  String get decoderAmp;

  /// No description provided for @headset.
  ///
  /// In en, this message translates to:
  /// **'Headset'**
  String get headset;

  /// No description provided for @bluetoothHeadphoneAmplifier.
  ///
  /// In en, this message translates to:
  /// **'Bluetooth & Headphone'**
  String get bluetoothHeadphoneAmplifier;

  /// No description provided for @player.
  ///
  /// In en, this message translates to:
  /// **'Player'**
  String get player;

  /// No description provided for @inputLinker.
  ///
  /// In en, this message translates to:
  /// **'Manually Enter Linker Control IP'**
  String get inputLinker;

  /// No description provided for @connect.
  ///
  /// In en, this message translates to:
  /// **'Connected'**
  String get connect;

  /// No description provided for @disconnected.
  ///
  /// In en, this message translates to:
  /// **'Disconnected'**
  String get disconnected;

  /// No description provided for @connecting.
  ///
  /// In en, this message translates to:
  /// **'Connecting'**
  String get connecting;

  /// No description provided for @disconnecting.
  ///
  /// In en, this message translates to:
  /// **'Disconnecting'**
  String get disconnecting;

  /// No description provided for @disconnect.
  ///
  /// In en, this message translates to:
  /// **'Not Connected'**
  String get disconnect;

  /// No description provided for @personalCenter.
  ///
  /// In en, this message translates to:
  /// **'Personal Center'**
  String get personalCenter;

  /// No description provided for @editPersonalInfo.
  ///
  /// In en, this message translates to:
  /// **'Edit Personal Information'**
  String get editPersonalInfo;

  /// No description provided for @accountSecurity.
  ///
  /// In en, this message translates to:
  /// **'Account Security Settings'**
  String get accountSecurity;

  /// No description provided for @avatar.
  ///
  /// In en, this message translates to:
  /// **'Avatar'**
  String get avatar;

  /// No description provided for @gender.
  ///
  /// In en, this message translates to:
  /// **'Gender'**
  String get gender;

  /// No description provided for @secret.
  ///
  /// In en, this message translates to:
  /// **'Secret'**
  String get secret;

  /// No description provided for @male.
  ///
  /// In en, this message translates to:
  /// **'Male'**
  String get male;

  /// No description provided for @female.
  ///
  /// In en, this message translates to:
  /// **'Female'**
  String get female;

  /// No description provided for @birthday.
  ///
  /// In en, this message translates to:
  /// **'Birthday'**
  String get birthday;

  /// No description provided for @signature.
  ///
  /// In en, this message translates to:
  /// **'Signature'**
  String get signature;

  /// No description provided for @region.
  ///
  /// In en, this message translates to:
  /// **'Region'**
  String get region;

  /// No description provided for @nickname.
  ///
  /// In en, this message translates to:
  /// **'Nickname'**
  String get nickname;

  /// Save button text
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// No description provided for @success.
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get success;

  /// No description provided for @updateSuccess.
  ///
  /// In en, this message translates to:
  /// **'Update Successful'**
  String get updateSuccess;

  /// No description provided for @userNotFound.
  ///
  /// In en, this message translates to:
  /// **'User Not Found'**
  String get userNotFound;

  /// No description provided for @loginPasswordModify.
  ///
  /// In en, this message translates to:
  /// **'Modify Login Password'**
  String get loginPasswordModify;

  /// No description provided for @accountAndBinding.
  ///
  /// In en, this message translates to:
  /// **'Account and Binding Settings'**
  String get accountAndBinding;

  /// No description provided for @modifyPhone.
  ///
  /// In en, this message translates to:
  /// **'Modify Phone Number'**
  String get modifyPhone;

  /// No description provided for @cancelAccount.
  ///
  /// In en, this message translates to:
  /// **'Cancel Account'**
  String get cancelAccount;

  /// No description provided for @modifyPassword.
  ///
  /// In en, this message translates to:
  /// **'Modify Password'**
  String get modifyPassword;

  /// No description provided for @oldPassword.
  ///
  /// In en, this message translates to:
  /// **'Old Password'**
  String get oldPassword;

  /// No description provided for @newPassword.
  ///
  /// In en, this message translates to:
  /// **'New Password'**
  String get newPassword;

  /// No description provided for @confirmNewPassword.
  ///
  /// In en, this message translates to:
  /// **'Confirm New Password'**
  String get confirmNewPassword;

  /// No description provided for @modifyPasswordSuccess.
  ///
  /// In en, this message translates to:
  /// **'Password Modified Successfully'**
  String get modifyPasswordSuccess;

  /// No description provided for @informationIncomplete.
  ///
  /// In en, this message translates to:
  /// **'Incomplete Information'**
  String get informationIncomplete;

  /// No description provided for @oldPasswordError.
  ///
  /// In en, this message translates to:
  /// **'Incorrect Old Password'**
  String get oldPasswordError;

  /// No description provided for @newPasswordAndConfirmPasswordNotConsistent.
  ///
  /// In en, this message translates to:
  /// **'New Password and Confirm Password Do Not Match'**
  String get newPasswordAndConfirmPasswordNotConsistent;

  /// No description provided for @oldPasswordAndNewPasswordCannotBeTheSame.
  ///
  /// In en, this message translates to:
  /// **'Old Password and New Password Cannot Be the Same'**
  String get oldPasswordAndNewPasswordCannotBeTheSame;

  /// No description provided for @newPasswordCannotBeTheSameAsTheOldPassword.
  ///
  /// In en, this message translates to:
  /// **'New Password Cannot Be the Same as the Old Password'**
  String get newPasswordCannotBeTheSameAsTheOldPassword;

  /// No description provided for @emailBinding.
  ///
  /// In en, this message translates to:
  /// **'Email Binding'**
  String get emailBinding;

  /// No description provided for @notBound.
  ///
  /// In en, this message translates to:
  /// **'Not Bound'**
  String get notBound;

  /// No description provided for @bound.
  ///
  /// In en, this message translates to:
  /// **'Bound'**
  String get bound;

  /// No description provided for @nowEmailVerification.
  ///
  /// In en, this message translates to:
  /// **'Verify Current Email'**
  String get nowEmailVerification;

  /// No description provided for @emailVerificationPrompt.
  ///
  /// In en, this message translates to:
  /// **'Please enter the email you want to bind. You can later change the binding or recover your password via email.'**
  String get emailVerificationPrompt;

  /// No description provided for @email.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// No description provided for @sendVerificationCode.
  ///
  /// In en, this message translates to:
  /// **'Send Verification Code'**
  String get sendVerificationCode;

  /// No description provided for @smsVerificationCode.
  ///
  /// In en, this message translates to:
  /// **'SMS Verification Code'**
  String get smsVerificationCode;

  /// No description provided for @verificationCodeHasBeenSent.
  ///
  /// In en, this message translates to:
  /// **'Verification Code Sent'**
  String get verificationCodeHasBeenSent;

  /// No description provided for @accountBindingSuccess.
  ///
  /// In en, this message translates to:
  /// **'Account Successfully Bound'**
  String get accountBindingSuccess;

  /// No description provided for @emailEmpty.
  ///
  /// In en, this message translates to:
  /// **'Email Cannot Be Empty'**
  String get emailEmpty;

  /// No description provided for @invalidEmail.
  ///
  /// In en, this message translates to:
  /// **'Invalid Email'**
  String get invalidEmail;

  /// No description provided for @emailBindSuccess.
  ///
  /// In en, this message translates to:
  /// **'Email Bound Successfully'**
  String get emailBindSuccess;

  /// No description provided for @unbindEmail.
  ///
  /// In en, this message translates to:
  /// **'Unbind Email'**
  String get unbindEmail;

  /// No description provided for @unbindEmailConfirm.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to unbind the email?'**
  String get unbindEmailConfirm;

  /// No description provided for @emailUnbindSuccess.
  ///
  /// In en, this message translates to:
  /// **'Email Unbound Successfully'**
  String get emailUnbindSuccess;

  /// No description provided for @boundEmail.
  ///
  /// In en, this message translates to:
  /// **'Bound Email'**
  String get boundEmail;

  /// No description provided for @unbind.
  ///
  /// In en, this message translates to:
  /// **'Unbind'**
  String get unbind;

  /// No description provided for @bind.
  ///
  /// In en, this message translates to:
  /// **'Bind'**
  String get bind;

  /// No description provided for @getEmailVerificationCode.
  ///
  /// In en, this message translates to:
  /// **'Get Email Verification Code'**
  String get getEmailVerificationCode;

  /// No description provided for @bindNewPhone.
  ///
  /// In en, this message translates to:
  /// **'Bind New Phone'**
  String get bindNewPhone;

  /// No description provided for @bindNewPhonePrompt.
  ///
  /// In en, this message translates to:
  /// **'Please enter your new phone number and click to receive a verification code for validation.'**
  String get bindNewPhonePrompt;

  /// No description provided for @bindNewPhoneSuccess.
  ///
  /// In en, this message translates to:
  /// **'New Phone Bound Successfully'**
  String get bindNewPhoneSuccess;

  /// No description provided for @bindNewPhoneFailure.
  ///
  /// In en, this message translates to:
  /// **'Failed to Bind New Phone'**
  String get bindNewPhoneFailure;

  /// No description provided for @bindNewPhoneFailurePrompt.
  ///
  /// In en, this message translates to:
  /// **'Failed to bind new phone. Please try again later.'**
  String get bindNewPhoneFailurePrompt;

  /// No description provided for @will.
  ///
  /// In en, this message translates to:
  /// **'Will'**
  String get will;

  /// No description provided for @accountWillBeCancelled.
  ///
  /// In en, this message translates to:
  /// **'The linked account will be cancelled'**
  String get accountWillBeCancelled;

  /// No description provided for @cancellationInstructions.
  ///
  /// In en, this message translates to:
  /// **'Note: Account cancellation is only supported by the account owner and cannot be restored after cancellation. Please proceed cautiously. After formal cancellation, you will no longer be able to use this account, nor retrieve any content or information related to this account, including but not limited to:\n\n1. Personal information under this account (including but not limited to avatar, nickname, bound products, etc.)\n2. All rights under this account (including but not limited to MQA membership, forum points, etc.)\n\n3. All playlists and other privileges under this account\n\n4. Other potential consequences resulting from account cancellation'**
  String get cancellationInstructions;

  /// No description provided for @cancellation.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancellation;

  /// No description provided for @confirmCancellation.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to cancel the account?'**
  String get confirmCancellation;

  /// No description provided for @cancellationSuccess.
  ///
  /// In en, this message translates to:
  /// **'Cancellation Successful'**
  String get cancellationSuccess;

  /// No description provided for @cancellationFailure.
  ///
  /// In en, this message translates to:
  /// **'Cancellation Failed'**
  String get cancellationFailure;

  /// No description provided for @exitApp.
  ///
  /// In en, this message translates to:
  /// **'Exit App'**
  String get exitApp;

  /// No description provided for @exitAppPrompt.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to exit the app?'**
  String get exitAppPrompt;

  /// No description provided for @phoneUpdateSuccess.
  ///
  /// In en, this message translates to:
  /// **'Phone Number Modified Successfully'**
  String get phoneUpdateSuccess;

  /// No description provided for @phoneEmpty.
  ///
  /// In en, this message translates to:
  /// **'Phone Number Cannot Be Empty'**
  String get phoneEmpty;

  /// No description provided for @verificationCodeEmpty.
  ///
  /// In en, this message translates to:
  /// **'Verification Code Cannot Be Empty'**
  String get verificationCodeEmpty;

  /// No description provided for @samePhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'New Phone Number Cannot Be the Same as the Original Phone Number'**
  String get samePhoneNumber;

  /// No description provided for @verifyOldPhone.
  ///
  /// In en, this message translates to:
  /// **'Verify Original Phone Number'**
  String get verifyOldPhone;

  /// No description provided for @setNewPhone.
  ///
  /// In en, this message translates to:
  /// **'Set New Phone Number'**
  String get setNewPhone;

  /// No description provided for @oldPhone.
  ///
  /// In en, this message translates to:
  /// **'Original Phone Number'**
  String get oldPhone;

  /// No description provided for @newPhone.
  ///
  /// In en, this message translates to:
  /// **'New Phone Number'**
  String get newPhone;

  /// No description provided for @passwordTooShort.
  ///
  /// In en, this message translates to:
  /// **'Password Too Short'**
  String get passwordTooShort;

  /// No description provided for @passwordEmpty.
  ///
  /// In en, this message translates to:
  /// **'Password Cannot Be Empty'**
  String get passwordEmpty;

  /// No description provided for @passwordUpdateSuccess.
  ///
  /// In en, this message translates to:
  /// **'Password Modified Successfully'**
  String get passwordUpdateSuccess;

  /// No description provided for @passwordUpdateSuccessRelogin.
  ///
  /// In en, this message translates to:
  /// **'Password Modified Successfully, please log in again'**
  String get passwordUpdateSuccessRelogin;

  /// No description provided for @scanning.
  ///
  /// In en, this message translates to:
  /// **'Scanning'**
  String get scanning;

  /// No description provided for @scanningDevicesHint.
  ///
  /// In en, this message translates to:
  /// **'Scanning for nearby Bluetooth devices...'**
  String get scanningDevicesHint;

  /// No description provided for @startScanningHint.
  ///
  /// In en, this message translates to:
  /// **'Start Scanning'**
  String get startScanningHint;

  /// No description provided for @manualInputIP.
  ///
  /// In en, this message translates to:
  /// **'Manual Input IP'**
  String get manualInputIP;

  /// No description provided for @manualInputIPHint.
  ///
  /// In en, this message translates to:
  /// **'Please enter the Linker control IP'**
  String get manualInputIPHint;

  /// No description provided for @bluetoothAndDac.
  ///
  /// In en, this message translates to:
  /// **'Bluetooth & DAC'**
  String get bluetoothAndDac;

  /// No description provided for @streamer.
  ///
  /// In en, this message translates to:
  /// **'Player'**
  String get streamer;

  /// No description provided for @pleaseInputIP.
  ///
  /// In en, this message translates to:
  /// **'Please enter the IP'**
  String get pleaseInputIP;

  /// No description provided for @deviceNotFoundHint1.
  ///
  /// In en, this message translates to:
  /// **'1. Please ensure the device is powered on.'**
  String get deviceNotFoundHint1;

  /// No description provided for @deviceNotFoundHint2.
  ///
  /// In en, this message translates to:
  /// **'2. Please keep the device properly connected to your phone\'\'s Bluetooth'**
  String get deviceNotFoundHint2;

  /// No description provided for @deviceNotFoundHint3.
  ///
  /// In en, this message translates to:
  /// **'3. After completing the above steps, click the refresh button to search again.'**
  String get deviceNotFoundHint3;

  /// No description provided for @deviceNotFoundHint4.
  ///
  /// In en, this message translates to:
  /// **'4. Some phone systems may require enabling the location service for Bluetooth BLE connections. You can try enabling the location service and refreshing again.'**
  String get deviceNotFoundHint4;

  /// No description provided for @invalidQRCode.
  ///
  /// In en, this message translates to:
  /// **'Invalid QR Code'**
  String get invalidQRCode;

  /// No description provided for @scanQRCode.
  ///
  /// In en, this message translates to:
  /// **'Scan QR Code'**
  String get scanQRCode;

  /// No description provided for @scanQRCodeHint.
  ///
  /// In en, this message translates to:
  /// **'Please scan the QR code'**
  String get scanQRCodeHint;

  /// No description provided for @scanQRCodeBottomHint.
  ///
  /// In en, this message translates to:
  /// **'Place the QR code in the box to scan automatically'**
  String get scanQRCodeBottomHint;

  /// No description provided for @noDevicesFound.
  ///
  /// In en, this message translates to:
  /// **'No Devices Found'**
  String get noDevicesFound;

  /// No description provided for @discoveredDevices.
  ///
  /// In en, this message translates to:
  /// **'Discovered Devices'**
  String get discoveredDevices;

  /// No description provided for @discoveredDevicesHint.
  ///
  /// In en, this message translates to:
  /// **'Found Multiple Discovered Devices'**
  String get discoveredDevicesHint;

  /// No description provided for @tapToConnect.
  ///
  /// In en, this message translates to:
  /// **'Tap to Connect'**
  String get tapToConnect;

  /// No description provided for @availableDevices.
  ///
  /// In en, this message translates to:
  /// **'Available Devices'**
  String get availableDevices;

  /// No description provided for @languageCode.
  ///
  /// In en, this message translates to:
  /// **'en'**
  String get languageCode;

  /// No description provided for @deviceNotFound.
  ///
  /// In en, this message translates to:
  /// **'Device Not Found'**
  String get deviceNotFound;

  /// No description provided for @connected.
  ///
  /// In en, this message translates to:
  /// **'Connected'**
  String get connected;

  /// No description provided for @battery.
  ///
  /// In en, this message translates to:
  /// **'Battery'**
  String get battery;

  /// No description provided for @tryAgain.
  ///
  /// In en, this message translates to:
  /// **'Try Again'**
  String get tryAgain;

  /// No description provided for @volumeControl.
  ///
  /// In en, this message translates to:
  /// **'Volume Control'**
  String get volumeControl;

  /// No description provided for @unlock.
  ///
  /// In en, this message translates to:
  /// **'Unlock'**
  String get unlock;

  /// No description provided for @lock.
  ///
  /// In en, this message translates to:
  /// **'Lock'**
  String get lock;

  /// No description provided for @audioSettings.
  ///
  /// In en, this message translates to:
  /// **'Audio Settings'**
  String get audioSettings;

  /// No description provided for @displayView.
  ///
  /// In en, this message translates to:
  /// **'Display View'**
  String get displayView;

  /// No description provided for @low.
  ///
  /// In en, this message translates to:
  /// **'Low'**
  String get low;

  /// No description provided for @middle.
  ///
  /// In en, this message translates to:
  /// **'Mid'**
  String get middle;

  /// No description provided for @high.
  ///
  /// In en, this message translates to:
  /// **'High'**
  String get high;

  /// No description provided for @equalizer.
  ///
  /// In en, this message translates to:
  /// **'Equalizer'**
  String get equalizer;

  /// No description provided for @eqPreset.
  ///
  /// In en, this message translates to:
  /// **'EQ Preset'**
  String get eqPreset;

  /// No description provided for @digitalFilter.
  ///
  /// In en, this message translates to:
  /// **'Digital Filter'**
  String get digitalFilter;

  /// No description provided for @channelBalance.
  ///
  /// In en, this message translates to:
  /// **'Channel Balance'**
  String get channelBalance;

  /// No description provided for @dsdMode.
  ///
  /// In en, this message translates to:
  /// **'DSD Mode'**
  String get dsdMode;

  /// No description provided for @systemSettings.
  ///
  /// In en, this message translates to:
  /// **'System Settings'**
  String get systemSettings;

  /// No description provided for @displayBrightness.
  ///
  /// In en, this message translates to:
  /// **'Display Brightness'**
  String get displayBrightness;

  /// No description provided for @displayTimeout.
  ///
  /// In en, this message translates to:
  /// **'Display Timeout'**
  String get displayTimeout;

  /// No description provided for @autoPowerOff.
  ///
  /// In en, this message translates to:
  /// **'Auto Power Off'**
  String get autoPowerOff;

  /// Disabled state
  ///
  /// In en, this message translates to:
  /// **'Disabled'**
  String get disabled;

  /// No description provided for @ledIndicator.
  ///
  /// In en, this message translates to:
  /// **'LED Indicator'**
  String get ledIndicator;

  /// No description provided for @connectToAccessSettings.
  ///
  /// In en, this message translates to:
  /// **'Connect to Access Settings'**
  String get connectToAccessSettings;

  /// No description provided for @connectionInfo.
  ///
  /// In en, this message translates to:
  /// **'Connection Information'**
  String get connectionInfo;

  /// No description provided for @codec.
  ///
  /// In en, this message translates to:
  /// **'Codec'**
  String get codec;

  /// No description provided for @deviceInfo.
  ///
  /// In en, this message translates to:
  /// **'Device Information'**
  String get deviceInfo;

  /// No description provided for @firmwareVersion.
  ///
  /// In en, this message translates to:
  /// **'Firmware Version'**
  String get firmwareVersion;

  /// No description provided for @serialNumber.
  ///
  /// In en, this message translates to:
  /// **'Serial Number'**
  String get serialNumber;

  /// No description provided for @totalPlayTime.
  ///
  /// In en, this message translates to:
  /// **'Total Play Time'**
  String get totalPlayTime;

  /// No description provided for @deleteDevice.
  ///
  /// In en, this message translates to:
  /// **'Delete Device'**
  String get deleteDevice;

  /// No description provided for @deleteDeviceConfirm.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete the device?'**
  String get deleteDeviceConfirm;

  /// Delete button text
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// No description provided for @preamplifier.
  ///
  /// In en, this message translates to:
  /// **'Preamplifier'**
  String get preamplifier;

  /// No description provided for @decodeModeDac.
  ///
  /// In en, this message translates to:
  /// **'DAC'**
  String get decodeModeDac;

  /// No description provided for @displayNormal.
  ///
  /// In en, this message translates to:
  /// **'Normal'**
  String get displayNormal;

  /// No description provided for @displayVu.
  ///
  /// In en, this message translates to:
  /// **'VU'**
  String get displayVu;

  /// No description provided for @displayFft.
  ///
  /// In en, this message translates to:
  /// **'FFT'**
  String get displayFft;

  /// No description provided for @inputUsb.
  ///
  /// In en, this message translates to:
  /// **'USB'**
  String get inputUsb;

  /// No description provided for @inputOptical.
  ///
  /// In en, this message translates to:
  /// **'Optical'**
  String get inputOptical;

  /// No description provided for @inputCoaxial.
  ///
  /// In en, this message translates to:
  /// **'Coaxial'**
  String get inputCoaxial;

  /// No description provided for @inputBluetooth.
  ///
  /// In en, this message translates to:
  /// **'Bluetooth'**
  String get inputBluetooth;

  /// No description provided for @languageZh.
  ///
  /// In en, this message translates to:
  /// **'Chinese'**
  String get languageZh;

  /// No description provided for @languageEn.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get languageEn;

  /// No description provided for @headphoneGainHigh.
  ///
  /// In en, this message translates to:
  /// **'High Gain'**
  String get headphoneGainHigh;

  /// No description provided for @headphoneGainLow.
  ///
  /// In en, this message translates to:
  /// **'Low Gain'**
  String get headphoneGainLow;

  /// No description provided for @multiFunctionKeyInputSelect.
  ///
  /// In en, this message translates to:
  /// **'Input Select'**
  String get multiFunctionKeyInputSelect;

  /// No description provided for @multiFunctionKeyLineOutSelect.
  ///
  /// In en, this message translates to:
  /// **'Line Out Select'**
  String get multiFunctionKeyLineOutSelect;

  /// No description provided for @multiFunctionKeyHeadphoneOutSelect.
  ///
  /// In en, this message translates to:
  /// **'Headphone Out Select'**
  String get multiFunctionKeyHeadphoneOutSelect;

  /// No description provided for @multiFunctionKeyHomeSelect.
  ///
  /// In en, this message translates to:
  /// **'Home Select'**
  String get multiFunctionKeyHomeSelect;

  /// No description provided for @multiFunctionKeyBrightnessSelect.
  ///
  /// In en, this message translates to:
  /// **'Brightness Select'**
  String get multiFunctionKeyBrightnessSelect;

  /// No description provided for @multiFunctionKeySleep.
  ///
  /// In en, this message translates to:
  /// **'Sleep'**
  String get multiFunctionKeySleep;

  /// No description provided for @multiFunctionKeyPcmFilterSelect.
  ///
  /// In en, this message translates to:
  /// **'PCM Filter Select'**
  String get multiFunctionKeyPcmFilterSelect;

  /// No description provided for @multiFunctionKeyMute.
  ///
  /// In en, this message translates to:
  /// **'Mute'**
  String get multiFunctionKeyMute;

  /// PEQ select function key
  ///
  /// In en, this message translates to:
  /// **'PEQ Select'**
  String get multiFunctionKeyPeqSelect;

  /// No description provided for @outputClose.
  ///
  /// In en, this message translates to:
  /// **'Off'**
  String get outputClose;

  /// No description provided for @outputSingleEnded.
  ///
  /// In en, this message translates to:
  /// **'RCA'**
  String get outputSingleEnded;

  /// No description provided for @outputBalanced.
  ///
  /// In en, this message translates to:
  /// **'XLR'**
  String get outputBalanced;

  /// No description provided for @outputSingleEndedAndBalanced.
  ///
  /// In en, this message translates to:
  /// **'RCA+XLR'**
  String get outputSingleEndedAndBalanced;

  /// No description provided for @powerTriggerSignal.
  ///
  /// In en, this message translates to:
  /// **'Signal'**
  String get powerTriggerSignal;

  /// No description provided for @powerTriggerVoltage.
  ///
  /// In en, this message translates to:
  /// **'12V'**
  String get powerTriggerVoltage;

  /// No description provided for @powerTriggerClose.
  ///
  /// In en, this message translates to:
  /// **'Off'**
  String get powerTriggerClose;

  /// No description provided for @screenBrightnessHigh.
  ///
  /// In en, this message translates to:
  /// **'High'**
  String get screenBrightnessHigh;

  /// No description provided for @screenBrightnessMedium.
  ///
  /// In en, this message translates to:
  /// **'Medium'**
  String get screenBrightnessMedium;

  /// No description provided for @screenBrightnessLow.
  ///
  /// In en, this message translates to:
  /// **'Low'**
  String get screenBrightnessLow;

  /// No description provided for @screenBrightnessAuto.
  ///
  /// In en, this message translates to:
  /// **'Auto'**
  String get screenBrightnessAuto;

  /// No description provided for @themeAurora.
  ///
  /// In en, this message translates to:
  /// **'Aurora'**
  String get themeAurora;

  /// No description provided for @themeOrange.
  ///
  /// In en, this message translates to:
  /// **'Orange'**
  String get themeOrange;

  /// No description provided for @themePeru.
  ///
  /// In en, this message translates to:
  /// **'Peru'**
  String get themePeru;

  /// No description provided for @themeGreen.
  ///
  /// In en, this message translates to:
  /// **'Bean Green'**
  String get themeGreen;

  /// No description provided for @themeKhaki.
  ///
  /// In en, this message translates to:
  /// **'Dark Khaki'**
  String get themeKhaki;

  /// No description provided for @themeRose.
  ///
  /// In en, this message translates to:
  /// **'Rose Brown'**
  String get themeRose;

  /// No description provided for @themeBlue.
  ///
  /// In en, this message translates to:
  /// **'Blue'**
  String get themeBlue;

  /// No description provided for @themePurple.
  ///
  /// In en, this message translates to:
  /// **'Fantasy Purple'**
  String get themePurple;

  /// No description provided for @themeWhite.
  ///
  /// In en, this message translates to:
  /// **'White'**
  String get themeWhite;

  /// No description provided for @darkMode.
  ///
  /// In en, this message translates to:
  /// **'Dark Mode'**
  String get darkMode;

  /// No description provided for @lightMode.
  ///
  /// In en, this message translates to:
  /// **'Light Mode'**
  String get lightMode;

  /// No description provided for @darkModeDescription.
  ///
  /// In en, this message translates to:
  /// **'Currently using dark theme'**
  String get darkModeDescription;

  /// No description provided for @lightModeDescription.
  ///
  /// In en, this message translates to:
  /// **'Currently using light theme'**
  String get lightModeDescription;

  /// No description provided for @usbTypeUac2.
  ///
  /// In en, this message translates to:
  /// **'UAC 2.0'**
  String get usbTypeUac2;

  /// No description provided for @usbTypeUac1.
  ///
  /// In en, this message translates to:
  /// **'UAC 1.0'**
  String get usbTypeUac1;

  /// No description provided for @deviceTypeBluetooth.
  ///
  /// In en, this message translates to:
  /// **'Bluetooth'**
  String get deviceTypeBluetooth;

  /// No description provided for @deviceTypeDac.
  ///
  /// In en, this message translates to:
  /// **'DAC'**
  String get deviceTypeDac;

  /// No description provided for @deviceTypeHeadphone.
  ///
  /// In en, this message translates to:
  /// **'Headphone'**
  String get deviceTypeHeadphone;

  /// No description provided for @deviceTypePlayer.
  ///
  /// In en, this message translates to:
  /// **'Player'**
  String get deviceTypePlayer;

  /// No description provided for @name.
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get name;

  /// No description provided for @volume.
  ///
  /// In en, this message translates to:
  /// **'Volume'**
  String get volume;

  /// No description provided for @headphoneOutput.
  ///
  /// In en, this message translates to:
  /// **'Headphone Output'**
  String get headphoneOutput;

  /// No description provided for @headphoneGain.
  ///
  /// In en, this message translates to:
  /// **'Headphone Gain'**
  String get headphoneGain;

  /// No description provided for @inputSelect.
  ///
  /// In en, this message translates to:
  /// **'Input Select'**
  String get inputSelect;

  /// No description provided for @outputSelect.
  ///
  /// In en, this message translates to:
  /// **'Output Select'**
  String get outputSelect;

  /// No description provided for @advanced.
  ///
  /// In en, this message translates to:
  /// **'Advanced'**
  String get advanced;

  /// No description provided for @advancedSettings.
  ///
  /// In en, this message translates to:
  /// **'Advanced Settings'**
  String get advancedSettings;

  /// No description provided for @editName.
  ///
  /// In en, this message translates to:
  /// **'Edit Name'**
  String get editName;

  /// No description provided for @enterNewName.
  ///
  /// In en, this message translates to:
  /// **'Enter New Name'**
  String get enterNewName;

  /// No description provided for @setting.
  ///
  /// In en, this message translates to:
  /// **'Setting'**
  String get setting;

  /// No description provided for @peq.
  ///
  /// In en, this message translates to:
  /// **'PEQ'**
  String get peq;

  /// No description provided for @guide.
  ///
  /// In en, this message translates to:
  /// **'Guide'**
  String get guide;

  /// No description provided for @theme.
  ///
  /// In en, this message translates to:
  /// **'Theme'**
  String get theme;

  /// No description provided for @powerTrigger.
  ///
  /// In en, this message translates to:
  /// **'Power Trigger'**
  String get powerTrigger;

  /// No description provided for @audioBalance.
  ///
  /// In en, this message translates to:
  /// **'Audio Balance'**
  String get audioBalance;

  /// No description provided for @filter.
  ///
  /// In en, this message translates to:
  /// **'Filter'**
  String get filter;

  /// No description provided for @decodeMode.
  ///
  /// In en, this message translates to:
  /// **'Decode Mode'**
  String get decodeMode;

  /// No description provided for @audioMonitoring.
  ///
  /// In en, this message translates to:
  /// **'Audio Bluetooth'**
  String get audioMonitoring;

  /// No description provided for @bluetoothAptx.
  ///
  /// In en, this message translates to:
  /// **'Bluetooth APTX'**
  String get bluetoothAptx;

  /// No description provided for @relay.
  ///
  /// In en, this message translates to:
  /// **'Remote Control'**
  String get relay;

  /// No description provided for @multiFunctionKey.
  ///
  /// In en, this message translates to:
  /// **'Custom Multi-function Button'**
  String get multiFunctionKey;

  /// No description provided for @usbMode.
  ///
  /// In en, this message translates to:
  /// **'USB Mode'**
  String get usbMode;

  /// No description provided for @screenBrightness.
  ///
  /// In en, this message translates to:
  /// **'Screen Brightness'**
  String get screenBrightness;

  /// No description provided for @language.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// No description provided for @resetSettings.
  ///
  /// In en, this message translates to:
  /// **'Reset Advanced Settings'**
  String get resetSettings;

  /// No description provided for @restoreFactorySettings.
  ///
  /// In en, this message translates to:
  /// **'Restore Factory Settings'**
  String get restoreFactorySettings;

  /// No description provided for @channel.
  ///
  /// In en, this message translates to:
  /// **'Channel'**
  String get channel;

  /// No description provided for @resetSettingsConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to reset the advanced settings?'**
  String get resetSettingsConfirmation;

  /// No description provided for @restoreFactorySettingsConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to restore the factory settings?'**
  String get restoreFactorySettingsConfirmation;

  /// No description provided for @import.
  ///
  /// In en, this message translates to:
  /// **'Import'**
  String get import;

  /// No description provided for @export.
  ///
  /// In en, this message translates to:
  /// **'Export'**
  String get export;

  /// No description provided for @target.
  ///
  /// In en, this message translates to:
  /// **'Target'**
  String get target;

  /// Source frequency response label
  ///
  /// In en, this message translates to:
  /// **'Source FR'**
  String get sourceFR;

  /// No description provided for @eachFilter.
  ///
  /// In en, this message translates to:
  /// **'Each Filter'**
  String get eachFilter;

  /// No description provided for @combinedFilter.
  ///
  /// In en, this message translates to:
  /// **'Combined Filter'**
  String get combinedFilter;

  /// No description provided for @filteredFR.
  ///
  /// In en, this message translates to:
  /// **'Filtered FR'**
  String get filteredFR;

  /// No description provided for @raw.
  ///
  /// In en, this message translates to:
  /// **'Raw'**
  String get raw;

  /// No description provided for @compensated.
  ///
  /// In en, this message translates to:
  /// **'Comp'**
  String get compensated;

  /// No description provided for @preAmplification.
  ///
  /// In en, this message translates to:
  /// **'Preamp'**
  String get preAmplification;

  /// No description provided for @gain.
  ///
  /// In en, this message translates to:
  /// **'Gain'**
  String get gain;

  /// No description provided for @peakingFilter.
  ///
  /// In en, this message translates to:
  /// **'Peaking Filter'**
  String get peakingFilter;

  /// No description provided for @lowPassFilter.
  ///
  /// In en, this message translates to:
  /// **'Low-Pass Filter'**
  String get lowPassFilter;

  /// No description provided for @highPassFilter.
  ///
  /// In en, this message translates to:
  /// **'High-Pass Filter'**
  String get highPassFilter;

  /// No description provided for @lowShelfFilter.
  ///
  /// In en, this message translates to:
  /// **'Low-Shelf Filter'**
  String get lowShelfFilter;

  /// No description provided for @highShelfFilter.
  ///
  /// In en, this message translates to:
  /// **'High-Shelf Filter'**
  String get highShelfFilter;

  /// No description provided for @centerFrequency.
  ///
  /// In en, this message translates to:
  /// **'Center Fre'**
  String get centerFrequency;

  /// No description provided for @connerFrequency.
  ///
  /// In en, this message translates to:
  /// **'Corner Frequency'**
  String get connerFrequency;

  /// No description provided for @q.
  ///
  /// In en, this message translates to:
  /// **'Q Factor'**
  String get q;

  /// No description provided for @frequency.
  ///
  /// In en, this message translates to:
  /// **'Frequency'**
  String get frequency;

  /// No description provided for @type.
  ///
  /// In en, this message translates to:
  /// **'Type'**
  String get type;

  /// No description provided for @addFilter.
  ///
  /// In en, this message translates to:
  /// **'Add Filter'**
  String get addFilter;

  /// No description provided for @mode.
  ///
  /// In en, this message translates to:
  /// **'Mode'**
  String get mode;

  /// No description provided for @import_target.
  ///
  /// In en, this message translates to:
  /// **'Import Target'**
  String get import_target;

  /// No description provided for @import_sourceFR.
  ///
  /// In en, this message translates to:
  /// **'Import Source FR'**
  String get import_sourceFR;

  /// No description provided for @selectDataFile.
  ///
  /// In en, this message translates to:
  /// **'Select Data File'**
  String get selectDataFile;

  /// No description provided for @feedbackContent.
  ///
  /// In en, this message translates to:
  /// **'Feedback Content'**
  String get feedbackContent;

  /// No description provided for @problemType.
  ///
  /// In en, this message translates to:
  /// **'Problem Type'**
  String get problemType;

  /// No description provided for @selectDeviceType.
  ///
  /// In en, this message translates to:
  /// **'Select Device Type'**
  String get selectDeviceType;

  /// No description provided for @device.
  ///
  /// In en, this message translates to:
  /// **'Device'**
  String get device;

  /// No description provided for @addPicture.
  ///
  /// In en, this message translates to:
  /// **'Add Picture (Optional)'**
  String get addPicture;

  /// No description provided for @contact.
  ///
  /// In en, this message translates to:
  /// **'Contact'**
  String get contact;

  /// No description provided for @feedbackTypeFeatureSuggestion.
  ///
  /// In en, this message translates to:
  /// **'Feature Suggestion'**
  String get feedbackTypeFeatureSuggestion;

  /// No description provided for @feedbackTypeBugReport.
  ///
  /// In en, this message translates to:
  /// **'Bug Report'**
  String get feedbackTypeBugReport;

  /// No description provided for @feedbackTypeUIImprovement.
  ///
  /// In en, this message translates to:
  /// **'UI Improvement'**
  String get feedbackTypeUIImprovement;

  /// No description provided for @feedbackTypeOther.
  ///
  /// In en, this message translates to:
  /// **'Other'**
  String get feedbackTypeOther;

  /// No description provided for @checkUpdate.
  ///
  /// In en, this message translates to:
  /// **'Check for Updates'**
  String get checkUpdate;

  /// No description provided for @checking.
  ///
  /// In en, this message translates to:
  /// **'Checking...,'**
  String get checking;

  /// No description provided for @alreadyLatestVersion.
  ///
  /// In en, this message translates to:
  /// **'Already Latest Version'**
  String get alreadyLatestVersion;

  /// No description provided for @failed.
  ///
  /// In en, this message translates to:
  /// **'Check update failed'**
  String get failed;

  /// No description provided for @foundNewVersion.
  ///
  /// In en, this message translates to:
  /// **'New Version Available'**
  String get foundNewVersion;

  /// No description provided for @currentVersion.
  ///
  /// In en, this message translates to:
  /// **'Current Version'**
  String get currentVersion;

  /// No description provided for @newVersion.
  ///
  /// In en, this message translates to:
  /// **'New Version'**
  String get newVersion;

  /// No description provided for @updateContent.
  ///
  /// In en, this message translates to:
  /// **'Update Content:'**
  String get updateContent;

  /// No description provided for @later.
  ///
  /// In en, this message translates to:
  /// **'Later'**
  String get later;

  /// No description provided for @updateNow.
  ///
  /// In en, this message translates to:
  /// **'Update Now'**
  String get updateNow;

  /// No description provided for @downloading.
  ///
  /// In en, this message translates to:
  /// **'Downloading...'**
  String get downloading;

  /// No description provided for @downloadCompleted.
  ///
  /// In en, this message translates to:
  /// **'Download completed'**
  String get downloadCompleted;

  /// No description provided for @downloadFailed.
  ///
  /// In en, this message translates to:
  /// **'Download failed'**
  String get downloadFailed;

  /// No description provided for @storagePermissionDenied.
  ///
  /// In en, this message translates to:
  /// **'Storage permission denied'**
  String get storagePermissionDenied;

  /// No description provided for @cannotGetDownloadPath.
  ///
  /// In en, this message translates to:
  /// **'Cannot get download path'**
  String get cannotGetDownloadPath;

  /// No description provided for @checkUpdateFailed.
  ///
  /// In en, this message translates to:
  /// **'Check update failed'**
  String get checkUpdateFailed;

  /// No description provided for @installPermissionDenied.
  ///
  /// In en, this message translates to:
  /// **'Install permission denied'**
  String get installPermissionDenied;

  /// No description provided for @failedToLoadContent.
  ///
  /// In en, this message translates to:
  /// **'Failed to load content'**
  String get failedToLoadContent;

  /// No description provided for @lastUpdated.
  ///
  /// In en, this message translates to:
  /// **'Last Updated'**
  String get lastUpdated;

  /// No description provided for @termsAndConditions.
  ///
  /// In en, this message translates to:
  /// **'Terms and Conditions'**
  String get termsAndConditions;

  /// No description provided for @agreementDialogContent.
  ///
  /// In en, this message translates to:
  /// **'By clicking Agree, you agree to the User Agreement and Privacy Policy'**
  String get agreementDialogContent;

  /// No description provided for @userAgreement.
  ///
  /// In en, this message translates to:
  /// **'User Agreement'**
  String get userAgreement;

  /// No description provided for @privacyPolicyText.
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicyText;

  /// No description provided for @disagree.
  ///
  /// In en, this message translates to:
  /// **'Disagree'**
  String get disagree;

  /// No description provided for @exportSuccess.
  ///
  /// In en, this message translates to:
  /// **'Export Successful'**
  String get exportSuccess;

  /// No description provided for @fileSavedTo.
  ///
  /// In en, this message translates to:
  /// **'File saved to:'**
  String get fileSavedTo;

  /// No description provided for @fileContentPreview.
  ///
  /// In en, this message translates to:
  /// **'File content preview:'**
  String get fileContentPreview;

  /// No description provided for @ok.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// No description provided for @openFileLocation.
  ///
  /// In en, this message translates to:
  /// **'Open File Location'**
  String get openFileLocation;

  /// No description provided for @sharedFilterFile.
  ///
  /// In en, this message translates to:
  /// **'Shared Filter File'**
  String get sharedFilterFile;

  /// No description provided for @sharedFile.
  ///
  /// In en, this message translates to:
  /// **'Shared File'**
  String get sharedFile;

  /// No description provided for @cannotOpenFileOrItsDirectory.
  ///
  /// In en, this message translates to:
  /// **'Cannot open file or its directory'**
  String get cannotOpenFileOrItsDirectory;

  /// No description provided for @errorOpeningFileLocation.
  ///
  /// In en, this message translates to:
  /// **'Error opening file location:'**
  String get errorOpeningFileLocation;

  /// No description provided for @errorSharingFile.
  ///
  /// In en, this message translates to:
  /// **'Error sharing file'**
  String get errorSharingFile;

  /// No description provided for @cannotParseSelectedFile.
  ///
  /// In en, this message translates to:
  /// **'Cannot parse selected file. Please ensure it is a valid CSV format and contains frequency and response data.'**
  String get cannotParseSelectedFile;

  /// No description provided for @cannotExtractValidFrequencyAndResponseDataFromCSV.
  ///
  /// In en, this message translates to:
  /// **'Cannot extract valid frequency and response data from CSV.'**
  String get cannotExtractValidFrequencyAndResponseDataFromCSV;

  /// No description provided for @importSuccess.
  ///
  /// In en, this message translates to:
  /// **'Import Successful'**
  String get importSuccess;

  /// No description provided for @successfullyImportedFile.
  ///
  /// In en, this message translates to:
  /// **'Successfully imported file'**
  String get successfullyImportedFile;

  /// No description provided for @errorImportingFile.
  ///
  /// In en, this message translates to:
  /// **'Error importing file'**
  String get errorImportingFile;

  /// No description provided for @noDataToExport.
  ///
  /// In en, this message translates to:
  /// **'No data to export.'**
  String get noDataToExport;

  /// No description provided for @fileSavedToAppFolder.
  ///
  /// In en, this message translates to:
  /// **'File saved to app folder:'**
  String get fileSavedToAppFolder;

  /// No description provided for @errorSavingFile.
  ///
  /// In en, this message translates to:
  /// **'Error saving file:'**
  String get errorSavingFile;

  /// No description provided for @saveMergedFilterFile.
  ///
  /// In en, this message translates to:
  /// **'Save Merged Filter File'**
  String get saveMergedFilterFile;

  /// No description provided for @startUsing.
  ///
  /// In en, this message translates to:
  /// **'Start Using'**
  String get startUsing;

  /// No description provided for @nextPage.
  ///
  /// In en, this message translates to:
  /// **'Next Page'**
  String get nextPage;

  /// No description provided for @background.
  ///
  /// In en, this message translates to:
  /// **'Background'**
  String get background;

  /// No description provided for @confirmDelete.
  ///
  /// In en, this message translates to:
  /// **'Confirm Delete'**
  String get confirmDelete;

  /// No description provided for @confirmDeleteHint.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this background image?'**
  String get confirmDeleteHint;

  /// No description provided for @opacity.
  ///
  /// In en, this message translates to:
  /// **'Opacity'**
  String get opacity;

  /// No description provided for @blur.
  ///
  /// In en, this message translates to:
  /// **'Blur'**
  String get blur;

  /// No description provided for @selectFromAlbum.
  ///
  /// In en, this message translates to:
  /// **'Select from Album'**
  String get selectFromAlbum;

  /// No description provided for @takePhoto.
  ///
  /// In en, this message translates to:
  /// **'Take Photo'**
  String get takePhoto;

  /// No description provided for @addBackground.
  ///
  /// In en, this message translates to:
  /// **'Add Background'**
  String get addBackground;

  /// No description provided for @deleteBackgroundFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to delete background'**
  String get deleteBackgroundFailed;

  /// No description provided for @saveBackgroundFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to save background'**
  String get saveBackgroundFailed;

  /// No description provided for @errorExportingFile.
  ///
  /// In en, this message translates to:
  /// **'Error exporting file'**
  String get errorExportingFile;

  /// No description provided for @skip.
  ///
  /// In en, this message translates to:
  /// **'Skip'**
  String get skip;

  /// No description provided for @version.
  ///
  /// In en, this message translates to:
  /// **'Version'**
  String get version;

  /// No description provided for @copyright.
  ///
  /// In en, this message translates to:
  /// **' 2025 Topping. All rights reserved.'**
  String get copyright;

  /// No description provided for @newFirmwareAvailable.
  ///
  /// In en, this message translates to:
  /// **'New Firmware Available'**
  String get newFirmwareAvailable;

  /// No description provided for @tip.
  ///
  /// In en, this message translates to:
  /// **'Tip'**
  String get tip;

  /// No description provided for @firmwareUpgrade.
  ///
  /// In en, this message translates to:
  /// **'Firmware Upgrade'**
  String get firmwareUpgrade;

  /// No description provided for @firmwareUpgrading.
  ///
  /// In en, this message translates to:
  /// **'Upgrading Firmware'**
  String get firmwareUpgrading;

  /// No description provided for @firmwareUpgradeSuccess.
  ///
  /// In en, this message translates to:
  /// **'Firmware Upgrade Success'**
  String get firmwareUpgradeSuccess;

  /// No description provided for @firmwareUpgradeFailed.
  ///
  /// In en, this message translates to:
  /// **'Firmware Upgrade Failed'**
  String get firmwareUpgradeFailed;

  /// No description provided for @firmwareUpgradeConfirm.
  ///
  /// In en, this message translates to:
  /// **'Are you sure to upgrade firmware?'**
  String get firmwareUpgradeConfirm;

  /// No description provided for @firmwareUpgradeWarning.
  ///
  /// In en, this message translates to:
  /// **'Please do not disconnect the device during upgrade'**
  String get firmwareUpgradeWarning;

  /// No description provided for @firmwareSize.
  ///
  /// In en, this message translates to:
  /// **'Firmware Size'**
  String get firmwareSize;

  /// No description provided for @firmwareDescription.
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get firmwareDescription;

  /// No description provided for @firmwareForceUpdate.
  ///
  /// In en, this message translates to:
  /// **'Force Update'**
  String get firmwareForceUpdate;

  /// No description provided for @firmwareDownloading.
  ///
  /// In en, this message translates to:
  /// **'Downloading Firmware'**
  String get firmwareDownloading;

  /// No description provided for @firmwareInstalling.
  ///
  /// In en, this message translates to:
  /// **'Installing Firmware'**
  String get firmwareInstalling;

  /// No description provided for @settingResetFail.
  ///
  /// In en, this message translates to:
  /// **'Reset Failed'**
  String get settingResetFail;

  /// Title for the firmware update page
  ///
  /// In en, this message translates to:
  /// **'Firmware Update'**
  String get firmwareUpdateTitle;

  /// No description provided for @firmwareUpdateNewFirmwareFound.
  ///
  /// In en, this message translates to:
  /// **'New Firmware Found'**
  String get firmwareUpdateNewFirmwareFound;

  /// No description provided for @firmwareUpdateFirmwareName.
  ///
  /// In en, this message translates to:
  /// **'Firmware Name'**
  String get firmwareUpdateFirmwareName;

  /// No description provided for @firmwareUpdateVersion.
  ///
  /// In en, this message translates to:
  /// **'Version'**
  String get firmwareUpdateVersion;

  /// No description provided for @firmwareUpdateDeviceModel.
  ///
  /// In en, this message translates to:
  /// **'Device Model'**
  String get firmwareUpdateDeviceModel;

  /// No description provided for @firmwareUpdateFileSize.
  ///
  /// In en, this message translates to:
  /// **'File Size'**
  String get firmwareUpdateFileSize;

  /// No description provided for @firmwareUpdateDescription.
  ///
  /// In en, this message translates to:
  /// **'Update Notes'**
  String get firmwareUpdateDescription;

  /// No description provided for @firmwareUpdateMandatoryUpdateNote.
  ///
  /// In en, this message translates to:
  /// **'* This version is a mandatory update'**
  String get firmwareUpdateMandatoryUpdateNote;

  /// No description provided for @firmwareUpdateStatus.
  ///
  /// In en, this message translates to:
  /// **'Update Status'**
  String get firmwareUpdateStatus;

  /// No description provided for @firmwareUpdateDownloading.
  ///
  /// In en, this message translates to:
  /// **'Downloading'**
  String get firmwareUpdateDownloading;

  /// No description provided for @firmwareUpdateUpgrading.
  ///
  /// In en, this message translates to:
  /// **'Upgrading'**
  String get firmwareUpdateUpgrading;

  /// No description provided for @firmwareUpdateDoNotDisconnect.
  ///
  /// In en, this message translates to:
  /// **'Please do not disconnect or turn off the device'**
  String get firmwareUpdateDoNotDisconnect;

  /// No description provided for @firmwareUpdateReadyToUpdate.
  ///
  /// In en, this message translates to:
  /// **'Ready to Update'**
  String get firmwareUpdateReadyToUpdate;

  /// No description provided for @firmwareUpdateCancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel Update'**
  String get firmwareUpdateCancel;

  /// No description provided for @firmwareUpdateStart.
  ///
  /// In en, this message translates to:
  /// **'Start Update'**
  String get firmwareUpdateStart;

  /// No description provided for @firmwareUpdateLatestVersion.
  ///
  /// In en, this message translates to:
  /// **'Already Latest Version'**
  String get firmwareUpdateLatestVersion;

  /// No description provided for @firmwareUpdateNoNeed.
  ///
  /// In en, this message translates to:
  /// **'No firmware update needed'**
  String get firmwareUpdateNoNeed;

  /// No description provided for @firmwareUpdateBack.
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get firmwareUpdateBack;

  /// No description provided for @firmwareUpdateSuccessTitle.
  ///
  /// In en, this message translates to:
  /// **'Update Successful'**
  String get firmwareUpdateSuccessTitle;

  /// No description provided for @firmwareUpdateSuccessMessage.
  ///
  /// In en, this message translates to:
  /// **'Device firmware updated to the latest version'**
  String get firmwareUpdateSuccessMessage;

  /// Message shown when firmware update succeeds and the device disconnects (likely rebooting)
  ///
  /// In en, this message translates to:
  /// **'Upgrade successful, device is rebooting...'**
  String get firmwareUpdateSuccessRebootMessage;

  /// No description provided for @firmwareUpdateDownloadingTitle.
  ///
  /// In en, this message translates to:
  /// **'Downloading'**
  String get firmwareUpdateDownloadingTitle;

  /// No description provided for @firmwareUpdateDownloadingMessage.
  ///
  /// In en, this message translates to:
  /// **'Downloading firmware file...'**
  String get firmwareUpdateDownloadingMessage;

  /// No description provided for @firmwareUpdateErrorTitle.
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get firmwareUpdateErrorTitle;

  /// No description provided for @firmwareUpdateDownloadFailed.
  ///
  /// In en, this message translates to:
  /// **'Firmware download failed'**
  String get firmwareUpdateDownloadFailed;

  /// No description provided for @firmwareUpdateUpgradingTitle.
  ///
  /// In en, this message translates to:
  /// **'Upgrading'**
  String get firmwareUpdateUpgradingTitle;

  /// No description provided for @firmwareUpdateUpgradingMessage.
  ///
  /// In en, this message translates to:
  /// **'Starting firmware upgrade...'**
  String get firmwareUpdateUpgradingMessage;

  /// No description provided for @firmwareUpdateSetDeviceFailed.
  ///
  /// In en, this message translates to:
  /// **'Set device failed'**
  String get firmwareUpdateSetDeviceFailed;

  /// No description provided for @firmwareUpdateErrorDuringUpdate.
  ///
  /// In en, this message translates to:
  /// **'Error during upgrade: {error}'**
  String firmwareUpdateErrorDuringUpdate(String error);

  /// No description provided for @firmwareUpdateCancelledTitle.
  ///
  /// In en, this message translates to:
  /// **'Cancelled'**
  String get firmwareUpdateCancelledTitle;

  /// No description provided for @firmwareUpdateCancelledMessage.
  ///
  /// In en, this message translates to:
  /// **'Firmware upgrade cancelled'**
  String get firmwareUpdateCancelledMessage;

  /// No description provided for @commonConfirm.
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get commonConfirm;

  /// Text for checking for firmware updates
  ///
  /// In en, this message translates to:
  /// **'Check Firmware Update'**
  String get checkFirmwareUpdate;

  /// Title for device reboot notification
  ///
  /// In en, this message translates to:
  /// **'Device Reboot'**
  String get deviceRebootTitle;

  /// Message content for device reboot notification
  ///
  /// In en, this message translates to:
  /// **'The device is restarting, please wait...'**
  String get deviceRebootMessage;

  /// Title for information dialog
  ///
  /// In en, this message translates to:
  /// **'Information'**
  String get infoTitle;

  /// No description provided for @errorOpeningFile.
  ///
  /// In en, this message translates to:
  /// **'Error opening file: {error}'**
  String errorOpeningFile(String error);

  /// Message shown when a file has been exported
  ///
  /// In en, this message translates to:
  /// **'File exported'**
  String get fileExported;

  /// Button text for sharing a file
  ///
  /// In en, this message translates to:
  /// **'Share File'**
  String get shareFile;

  /// No description provided for @eqWavePainterStarted.
  ///
  /// In en, this message translates to:
  /// **'EQ Wave Painter started. Size: {size}'**
  String eqWavePainterStarted(String size);

  /// No description provided for @targetRawDataPrepared.
  ///
  /// In en, this message translates to:
  /// **'Target raw data prepared (interpolated): {count} points'**
  String targetRawDataPrepared(int count);

  /// No description provided for @sourceFRRawDataPrepared.
  ///
  /// In en, this message translates to:
  /// **'Source FR raw data prepared (interpolated): {count} points'**
  String sourceFRRawDataPrepared(int count);

  /// No description provided for @combinedRawDataPrepared.
  ///
  /// In en, this message translates to:
  /// **'Combined raw data prepared: {count} points'**
  String combinedRawDataPrepared(int count);

  /// No description provided for @individualBandsRawDataPrepared.
  ///
  /// In en, this message translates to:
  /// **'Individual bands raw data prepared: {bandCount} bands, total points: {pointCount}'**
  String individualBandsRawDataPrepared(int bandCount, int pointCount);

  /// No description provided for @filteredFRRawDataPrepared.
  ///
  /// In en, this message translates to:
  /// **'Filtered FR raw data prepared (based on interpolated source): {count} points'**
  String filteredFRRawDataPrepared(int count);

  /// No description provided for @dynamicDbRangeCalculated.
  ///
  /// In en, this message translates to:
  /// **'Dynamic dB range calculated: Min={min}, Max={max}'**
  String dynamicDbRangeCalculated(String min, String max);

  /// Log message when coordinate converter is created
  ///
  /// In en, this message translates to:
  /// **'Coordinate converter created and chart height adjusted'**
  String get coordinateConverterCreated;

  /// Log message when grid and labels are drawn
  ///
  /// In en, this message translates to:
  /// **'Grid and labels drawn'**
  String get gridAndLabelsDrawn;

  /// Log message when drawing target curve
  ///
  /// In en, this message translates to:
  /// **'Drawing Target Curve...'**
  String get drawingTargetCurve;

  /// Log message when drawing source FR curve
  ///
  /// In en, this message translates to:
  /// **'Drawing Source FR Curve...'**
  String get drawingSourceFRCurve;

  /// Log message when drawing individual bands
  ///
  /// In en, this message translates to:
  /// **'Drawing Individual Bands...'**
  String get drawingIndividualBands;

  /// Log message when drawing combined curve
  ///
  /// In en, this message translates to:
  /// **'Drawing Combined Curve...'**
  String get drawingCombinedCurve;

  /// Log message when drawing filtered FR curve
  ///
  /// In en, this message translates to:
  /// **'Drawing Filtered FR Curve...'**
  String get drawingFilteredFRCurve;

  /// Log message when drawing flat line
  ///
  /// In en, this message translates to:
  /// **'Drawing Flat Line...'**
  String get drawingFlatLine;

  /// Log message when EQ wave painter finishes
  ///
  /// In en, this message translates to:
  /// **'EQ Wave Painter finished'**
  String get eqWavePainterFinished;

  /// No description provided for @finalDynamicDbRange.
  ///
  /// In en, this message translates to:
  /// **'Final Dynamic dB range: Min={min}, Max={max}'**
  String finalDynamicDbRange(String min, String max);

  /// PEQ settings page title
  ///
  /// In en, this message translates to:
  /// **'PEQ Settings'**
  String get peqSettings;

  /// Import and export section header
  ///
  /// In en, this message translates to:
  /// **'Import & Export'**
  String get importExport;

  /// Configuration management section header
  ///
  /// In en, this message translates to:
  /// **'Configuration Management'**
  String get configManagement;

  /// Add configuration dialog title
  ///
  /// In en, this message translates to:
  /// **'Add Configuration'**
  String get addConfig;

  /// Configuration name input label
  ///
  /// In en, this message translates to:
  /// **'Configuration Name'**
  String get configName;

  /// Description input label
  ///
  /// In en, this message translates to:
  /// **'Description (Optional)'**
  String get description;

  /// Configuration name hint text
  ///
  /// In en, this message translates to:
  /// **'e.g. Bass Boost, Clear Vocals, etc.'**
  String get configNameHint;

  /// Configuration description hint text
  ///
  /// In en, this message translates to:
  /// **'Briefly describe the purpose of this configuration'**
  String get configDescriptionHint;

  /// Add button text
  ///
  /// In en, this message translates to:
  /// **'Add'**
  String get add;

  /// Add new configuration tooltip
  ///
  /// In en, this message translates to:
  /// **'Add New Configuration'**
  String get addNewConfig;

  /// Import target button text
  ///
  /// In en, this message translates to:
  /// **'Import Target'**
  String get importTarget;

  /// Import source FR button text
  ///
  /// In en, this message translates to:
  /// **'Import Source FR'**
  String get importSourceFR;

  /// Export combined filter button text
  ///
  /// In en, this message translates to:
  /// **'Export Combined Filter'**
  String get exportCombinedFilter;

  /// Target frequency response label
  ///
  /// In en, this message translates to:
  /// **'Target FR'**
  String get targetFR;

  /// Edit configuration dialog title/tooltip
  ///
  /// In en, this message translates to:
  /// **'Edit Configuration'**
  String get editConfig;

  /// Delete configuration dialog title/tooltip
  ///
  /// In en, this message translates to:
  /// **'Delete Configuration'**
  String get deleteConfig;

  /// Delete configuration confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete the configuration \"{name}\"? This action cannot be undone.'**
  String deleteConfigConfirmation(Object name);

  /// Delete target file dialog title
  ///
  /// In en, this message translates to:
  /// **'Delete Target File'**
  String get deleteTargetFile;

  /// Delete source FR file dialog title
  ///
  /// In en, this message translates to:
  /// **'Delete Source FR File'**
  String get deleteSourceFRFile;

  /// Delete file confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete the file \"{name}\"? This action cannot be undone.'**
  String deleteFileConfirmation(Object name);

  /// Message shown when there are no configurations
  ///
  /// In en, this message translates to:
  /// **'No configurations yet. Click \"+\" to create a new one.'**
  String get noConfigsMessage;

  /// Device is powered off
  ///
  /// In en, this message translates to:
  /// **'Device is powered off'**
  String get devicePowerOff;

  /// Turn on the power to control the device
  ///
  /// In en, this message translates to:
  /// **'Turn on the power to control the device'**
  String get devicePowerOffHint;

  /// Power on button text
  ///
  /// In en, this message translates to:
  /// **'Power On'**
  String get powerOn;

  /// Optical 1 input
  ///
  /// In en, this message translates to:
  /// **'Optical 1'**
  String get inputOptical1;

  /// Optical 2 input
  ///
  /// In en, this message translates to:
  /// **'Optical 2'**
  String get inputOptical2;

  /// Coaxial 1 input
  ///
  /// In en, this message translates to:
  /// **'Coaxial 1'**
  String get inputCoaxial1;

  /// Coaxial 2 input
  ///
  /// In en, this message translates to:
  /// **'Coaxial 2'**
  String get inputCoaxial2;

  /// AES input
  ///
  /// In en, this message translates to:
  /// **'AES'**
  String get inputAes;

  /// IIS input
  ///
  /// In en, this message translates to:
  /// **'IIS'**
  String get inputIis;

  /// DAC output
  ///
  /// In en, this message translates to:
  /// **'DAC'**
  String get outputDac;

  /// Preamp output
  ///
  /// In en, this message translates to:
  /// **'Preamp'**
  String get outputPreamp;

  /// All outputs
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get outputAll;

  /// Auto mode
  ///
  /// In en, this message translates to:
  /// **'Auto'**
  String get auto;

  /// Enabled state
  ///
  /// In en, this message translates to:
  /// **'Enabled'**
  String get enabled;

  /// Standard mode
  ///
  /// In en, this message translates to:
  /// **'Standard'**
  String get standard;

  /// Inverted mode
  ///
  /// In en, this message translates to:
  /// **'Inverted'**
  String get inverted;

  /// Swapped mode
  ///
  /// In en, this message translates to:
  /// **'Swapped'**
  String get swapped;

  /// Signal display
  ///
  /// In en, this message translates to:
  /// **'Signal'**
  String get displaySignal;

  /// 12V display
  ///
  /// In en, this message translates to:
  /// **'12V'**
  String get display12V;

  /// Display off
  ///
  /// In en, this message translates to:
  /// **'Off'**
  String get displayOff;

  /// USB Type-C
  ///
  /// In en, this message translates to:
  /// **'Type-C'**
  String get usbTypeC;

  /// USB Type-B
  ///
  /// In en, this message translates to:
  /// **'Type-B'**
  String get usbTypeB;

  /// Power trigger off
  ///
  /// In en, this message translates to:
  /// **'Off'**
  String get powerTriggerOff;

  /// Power trigger 12V
  ///
  /// In en, this message translates to:
  /// **'12V'**
  String get powerTrigger12V;

  /// Output select function key
  ///
  /// In en, this message translates to:
  /// **'Output Select'**
  String get multiFunctionKeyOutputSelect;

  /// Screen off function key
  ///
  /// In en, this message translates to:
  /// **'Screen Off'**
  String get multiFunctionKeyScreenOff;

  /// No description provided for @usbSelect.
  ///
  /// In en, this message translates to:
  /// **'USB Select'**
  String get usbSelect;

  /// No description provided for @usbDsdPassthrough.
  ///
  /// In en, this message translates to:
  /// **'USB DSD Passthrough'**
  String get usbDsdPassthrough;

  /// No description provided for @iisPhase.
  ///
  /// In en, this message translates to:
  /// **'IIS Phase'**
  String get iisPhase;

  /// No description provided for @iisDsdChannel.
  ///
  /// In en, this message translates to:
  /// **'IIS DSD Channel'**
  String get iisDsdChannel;
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['en', 'ja', 'ko', 'zh'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {

  // Lookup logic when language+script codes are specified.
  switch (locale.languageCode) {
    case 'zh': {
  switch (locale.scriptCode) {
    case 'Hant': return AppLocalizationsZhHant();
   }
  break;
   }
  }

  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en': return AppLocalizationsEn();
    case 'ja': return AppLocalizationsJa();
    case 'ko': return AppLocalizationsKo();
    case 'zh': return AppLocalizationsZh();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
