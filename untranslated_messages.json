{"ja": ["failed", "eqWavePainterStarted", "targetRawDataPrepared", "sourceFRRawDataPrepared", "combinedRawDataPrepared", "individualBandsRawDataPrepared", "filteredFRRawDataPrepared", "dynamicDbRangeCalculated", "coordinateConverterCreated", "gridAndLabelsDrawn", "drawingTargetCurve", "drawingSourceFRCurve", "drawingIndividualBands", "drawingCombinedCurve", "drawingFilteredFRCurve", "drawingFlatLine", "eqWavePainterFinished", "finalDynamicDbRange"], "ko": ["failed", "eqWavePainterStarted", "targetRawDataPrepared", "sourceFRRawDataPrepared", "combinedRawDataPrepared", "individualBandsRawDataPrepared", "filteredFRRawDataPrepared", "dynamicDbRangeCalculated", "coordinateConverterCreated", "gridAndLabelsDrawn", "drawingTargetCurve", "drawingSourceFRCurve", "drawingIndividualBands", "drawingCombinedCurve", "drawingFilteredFRCurve", "drawingFlatLine", "eqWavePainterFinished", "finalDynamicDbRange"], "zh": ["failed"], "zh_Hant": ["failed", "eqWavePainterStarted", "targetRawDataPrepared", "sourceFRRawDataPrepared", "combinedRawDataPrepared", "individualBandsRawDataPrepared", "filteredFRRawDataPrepared", "dynamicDbRangeCalculated", "coordinateConverterCreated", "gridAndLabelsDrawn", "drawingTargetCurve", "drawingSourceFRCurve", "drawingIndividualBands", "drawingCombinedCurve", "drawingFilteredFRCurve", "drawingFlatLine", "eqWavePainterFinished", "finalDynamicDbRange"]}